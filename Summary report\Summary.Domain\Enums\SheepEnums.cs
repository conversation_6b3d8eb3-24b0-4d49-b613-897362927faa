namespace Summary.Domain.Enums
{
    /// <summary>
    /// 羊只性别枚举 - 用于标识羊只的性别
    /// </summary>
    public enum SheepGender : byte
    {
        /// <summary>
        /// 公羊 - 雄性羊只，用于配种
        /// </summary>
        Male = 1,
        
        /// <summary>
        /// 母羊 - 雌性羊只，用于产羔和生产
        /// </summary>
        Female = 2
    }

    /// <summary>
    /// 羊只健康状态枚举 - 用于标识羊只的健康状况
    /// </summary>
    public enum SheepHealthStatus
    {
        /// <summary>
        /// 健康 - 羊只身体状况良好，无疾病
        /// </summary>
        Healthy,
        
        /// <summary>
        /// 生病 - 羊只患有疾病，需要治疗
        /// </summary>
        Sick,
        
        /// <summary>
        /// 怀孕 - 母羊已怀孕，需要特殊照顾
        /// </summary>
        Pregnant,
        
        /// <summary>
        /// 康复中 - 羊只正在康复过程中
        /// </summary>
        Recovering
    }

    /// <summary>
    /// 配种成功状态枚举 - 用于标识配种是否成功
    /// </summary>
    public enum BreedingSuccess : byte
    {
        /// <summary>
        /// 失败 - 配种未成功，母羊未怀孕
        /// </summary>
        Failed = 0,
        
        /// <summary>
        /// 成功 - 配种成功，母羊已怀孕
        /// </summary>
        Success = 1
    }

    /// <summary>
    /// 报表导出格式枚举 - 用于指定报表的导出格式
    /// </summary>
    public enum ExportFormat
    {
        /// <summary>
        /// PDF格式 - 便携式文档格式，适合打印和分享
        /// </summary>
        PDF,
        
        /// <summary>
        /// Excel格式 - 电子表格格式，适合数据分析和编辑
        /// </summary>
        Excel,
        
        /// <summary>
        /// Word格式 - 文档格式，适合文字报告
        /// </summary>
        Word
    }
} 
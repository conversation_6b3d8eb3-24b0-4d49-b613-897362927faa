using MediatR;
using Summary.Domain.Entities;
using Summary.ErrorCode;

namespace Summary.API.Write.Application.command
{
    /// <summary>
    /// 添加生产记录命令
    /// </summary>
    public class AddProductionRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 母羊ID
        /// </summary>
        public string EweId { get; set; } = string.Empty;
        
        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime ProduceDate { get; set; }
        
        /// <summary>
        /// 生产数量
        /// </summary>
        public int ProduceCount { get; set; }
        
        /// <summary>
        /// 问题描述
        /// </summary>
        public string? Issues { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 更新生产记录命令
    /// </summary>
    public class UpdateProductionRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long RecordId { get; set; }
        
        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ProduceDate { get; set; }
        
        /// <summary>
        /// 生产数量
        /// </summary>
        public int? ProduceCount { get; set; }
        
        /// <summary>
        /// 问题描述
        /// </summary>
        public string? Issues { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 删除生产记录命令
    /// </summary>
    public class DeleteProductionRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long RecordId { get; set; }
    }
} 
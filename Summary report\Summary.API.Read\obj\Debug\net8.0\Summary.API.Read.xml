<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Summary.API.Read</name>
    </assembly>
    <members>
        <member name="T:Summary.API.Read.Application.command.GetBreedingStatisticsCommand">
            <summary>
            获取配种统计命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetBreedingStatisticsCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetBreedingStatisticsCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetBreedingStatisticsCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetBreedingStatisticsCommand.Breed">
            <summary>
            品种
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GenerateBreedingReportCommand">
            <summary>
            生成配种报表命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateBreedingReportCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateBreedingReportCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateBreedingReportCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateBreedingReportCommand.Format">
            <summary>
            报表格式 - Excel, PDF
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetInventoryStatisticsCommand">
            <summary>
            获取羊场存栏统计命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetInventoryStatisticsCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetInventoryStatisticsCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetInventoryStatisticsCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetInventoryHistoryTrendCommand">
            <summary>
            获取羊场存栏历史趋势命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetInventoryHistoryTrendCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetInventoryHistoryTrendCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetInventoryHistoryTrendCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GenerateInventoryReportCommand">
            <summary>
            生成羊场存栏报表命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateInventoryReportCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateInventoryReportCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateInventoryReportCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetLambingStatisticsCommand">
            <summary>
            获取产羔统计命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetLambingStatisticsCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetLambingStatisticsCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetLambingStatisticsCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetLambingStatisticsCommand.Breed">
            <summary>
            品种
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GenerateLambingReportCommand">
            <summary>
            生成产羔报表命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateLambingReportCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateLambingReportCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateLambingReportCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateLambingReportCommand.Format">
            <summary>
            报表格式 - Excel, PDF
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetProductionStatisticsCommand">
            <summary>
            获取生产统计命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetProductionStatisticsCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetProductionStatisticsCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetProductionStatisticsCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetProductionStatisticsCommand.Breed">
            <summary>
            品种
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GenerateProductionReportCommand">
            <summary>
            生成生产报表命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateProductionReportCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateProductionReportCommand.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateProductionReportCommand.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GenerateProductionReportCommand.Format">
            <summary>
            报表格式 - Excel, PDF
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetRealTimeInventoryCommand">
            <summary>
            获取羊场实时存栏命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetRealTimeInventoryCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetRealTimeInventoryCommand.DisplayMode">
            <summary>
            显示模式 - list: 列表模式, map: 地图模式
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetSheepLocationMapCommand">
            <summary>
            获取羊只位置地图命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetSheepLocationMapCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetSheepDetailsCommand">
            <summary>
            获取羊只详情命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetSheepDetailsCommand.SheepId">
            <summary>
            羊只ID
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetSheepCommand">
            <summary>
            获取所有羊只信息命令
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetSheepByIdCommand">
            <summary>
            根据ID获取羊只信息命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetSheepByIdCommand.SheepId">
            <summary>
            羊只ID
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetSheepByFarmCommand">
            <summary>
            根据农场ID获取羊只列表命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetSheepByFarmCommand.FarmId">
            <summary>
            农场ID
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.command.GetSheepByBreedCommand">
            <summary>
            根据品种获取羊只列表命令
            </summary>
        </member>
        <member name="P:Summary.API.Read.Application.command.GetSheepByBreedCommand.Breed">
            <summary>
            品种
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.Handler.BreedingStatisticsHandler">
            <summary>
            配种统计处理器
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.BreedingStatisticsHandler.Handle(Summary.API.Read.Application.command.GetBreedingStatisticsCommand,System.Threading.CancellationToken)">
            <summary>
            获取配种统计
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.BreedingStatisticsHandler.Handle(Summary.API.Read.Application.command.GenerateBreedingReportCommand,System.Threading.CancellationToken)">
            <summary>
            生成配种报表
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.Handler.InventoryStatisticsHandler">
            <summary>
            羊场存栏统计处理器
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.InventoryStatisticsHandler.Handle(Summary.API.Read.Application.command.GetInventoryStatisticsCommand,System.Threading.CancellationToken)">
            <summary>
            获取羊场存栏统计
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.InventoryStatisticsHandler.Handle(Summary.API.Read.Application.command.GetInventoryHistoryTrendCommand,System.Threading.CancellationToken)">
            <summary>
            获取羊场存栏历史趋势
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.InventoryStatisticsHandler.Handle(Summary.API.Read.Application.command.GenerateInventoryReportCommand,System.Threading.CancellationToken)">
            <summary>
            生成羊场存栏报表
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.InventoryStatisticsHandler.GetAgeGroup(System.DateTime)">
            <summary>
            获取年龄分组
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.Handler.LambingStatisticsHandler">
            <summary>
            产羔统计处理器
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.LambingStatisticsHandler.Handle(Summary.API.Read.Application.command.GetLambingStatisticsCommand,System.Threading.CancellationToken)">
            <summary>
            获取产羔统计
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.LambingStatisticsHandler.Handle(Summary.API.Read.Application.command.GenerateLambingReportCommand,System.Threading.CancellationToken)">
            <summary>
            生成产羔报表
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.Handler.ProductionStatisticsHandler">
            <summary>
            生产统计处理器
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.ProductionStatisticsHandler.Handle(Summary.API.Read.Application.command.GetProductionStatisticsCommand,System.Threading.CancellationToken)">
            <summary>
            获取生产统计
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.ProductionStatisticsHandler.Handle(Summary.API.Read.Application.command.GenerateProductionReportCommand,System.Threading.CancellationToken)">
            <summary>
            生成生产报表
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.Handler.RealTimeInventoryHandler">
            <summary>
            羊场实时存栏处理器
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.RealTimeInventoryHandler.Handle(Summary.API.Read.Application.command.GetRealTimeInventoryCommand,System.Threading.CancellationToken)">
            <summary>
            获取羊场实时存栏
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.RealTimeInventoryHandler.Handle(Summary.API.Read.Application.command.GetSheepLocationMapCommand,System.Threading.CancellationToken)">
            <summary>
            获取羊只位置地图
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.RealTimeInventoryHandler.Handle(Summary.API.Read.Application.command.GetSheepDetailsCommand,System.Threading.CancellationToken)">
            <summary>
            获取羊只详情
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.RealTimeInventoryHandler.ParseLocation(System.String)">
            <summary>
            解析位置信息
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.RealTimeInventoryHandler.CalculateMapCenter(System.Collections.Generic.List{System.Object})">
            <summary>
            计算地图中心点
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.RealTimeInventoryHandler.CalculateAge(System.Nullable{System.DateTime})">
            <summary>
            计算年龄
            </summary>
        </member>
        <member name="T:Summary.API.Read.Application.Handler.SheepHandler">
            <summary>
            羊只查询处理器
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.SheepHandler.Handle(Summary.API.Read.Application.command.GetSheepCommand,System.Threading.CancellationToken)">
            <summary>
            获取所有羊只信息
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.SheepHandler.Handle(Summary.API.Read.Application.command.GetSheepByIdCommand,System.Threading.CancellationToken)">
            <summary>
            根据ID获取羊只信息
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.SheepHandler.Handle(Summary.API.Read.Application.command.GetSheepByFarmCommand,System.Threading.CancellationToken)">
            <summary>
            根据农场ID获取羊只列表
            </summary>
        </member>
        <member name="M:Summary.API.Read.Application.Handler.SheepHandler.Handle(Summary.API.Read.Application.command.GetSheepByBreedCommand,System.Threading.CancellationToken)">
            <summary>
            根据品种获取羊只列表
            </summary>
        </member>
        <member name="T:Summary.API.Read.Controllers.SheepController">
            <summary>
            羊只控制器
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.SheepController.GetSheep">
            <summary>
            获取所有羊只信息
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.SheepController.GetSheep(System.String)">
            <summary>
            根据ID获取羊只信息
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.SheepController.GetSheepByFarm(System.String)">
            <summary>
            根据农场ID获取羊只列表
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.SheepController.GetSheepByBreed(System.String)">
            <summary>
            根据品种获取羊只列表
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.SheepController.GetSheepDetails(System.String)">
            <summary>
            获取羊只的完整信息（包括相关记录）
            </summary>
        </member>
        <member name="T:Summary.API.Read.Controllers.StatisticsController">
            <summary>
            统计控制器
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GetInventoryStatistics(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取羊场存栏统计
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GetInventoryHistoryTrend(System.String,System.DateTime,System.DateTime)">
            <summary>
            获取羊场存栏历史趋势
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GenerateInventoryReport(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            生成羊场存栏报表
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GetRealTimeInventory(System.String,System.String)">
            <summary>
            获取羊场实时存栏
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GetSheepLocationMap(System.String)">
            <summary>
            获取羊只位置地图
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GetSheepDetails(System.String)">
            <summary>
            获取羊只详情
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GetLambingStatistics(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            获取产羔统计
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GenerateLambingReport(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            生成产羔报表
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GetBreedingStatistics(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            获取配种统计
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GenerateBreedingReport(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            生成配种报表
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GetProductionStatistics(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            获取生产统计
            </summary>
        </member>
        <member name="M:Summary.API.Read.Controllers.StatisticsController.GenerateProductionReport(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            生成生产报表
            </summary>
        </member>
    </members>
</doc>

// 数据转换工具函数 - 处理前后端数据格式转换
import type {
  Sheep,
  LambingRecord,
  BreedingRecord,
  ProductionRecord,
  InventoryStatistics,
  LambingStatistics,
  BreedingStatistics,
  ProductionStatistics,
  RealTimeInventory
} from '@/types'
import {
  GenderDisplayMap,
  HealthDisplayMap,
  BreedColorMap
} from '@/types'

/**
 * 将后端数据转换为前端格式
 */
export const transformBackendData = {
  // 转换羊只数据
  sheep(backendSheep: any): Sheep {
    return {
      sheepId: backendSheep.sheepId || backendSheep.SheepId,
      farmId: backendSheep.farmId || backendSheep.FarmId,
      breed: backendSheep.breed || backendSheep.Breed,
      gender: backendSheep.gender || backendSheep.Gender,
      birthDate: backendSheep.birthDate || backendSheep.BirthDate,
      health: backendSheep.health || backendSheep.Health,
      keeperId: backendSheep.keeperId || backendSheep.KeeperId,
      location: backendSheep.location || backendSheep.Location,
      lambingRecords: backendSheep.lambingRecords || backendSheep.LambingRecords,
      breedingRecordsAsEwe: backendSheep.breedingRecordsAsEwe || backendSheep.BreedingRecordsAsEwe,
      breedingRecordsAsRam: backendSheep.breedingRecordsAsRam || backendSheep.BreedingRecordsAsRam,
      productionRecords: backendSheep.productionRecords || backendSheep.ProductionRecords
    }
  },

  // 转换产羔记录数据
  lambingRecord(backendRecord: any): LambingRecord {
    return {
      recordId: backendRecord.recordId || backendRecord.RecordId,
      eweId: backendRecord.eweId || backendRecord.EweId,
      lambingDate: backendRecord.lambingDate || backendRecord.LambingDate,
      lambCount: backendRecord.lambCount || backendRecord.LambCount,
      survivalRate: backendRecord.survivalRate || backendRecord.SurvivalRate,
      ewe: backendRecord.ewe ? this.sheep(backendRecord.ewe) : undefined
    }
  },

  // 转换配种记录数据
  breedingRecord(backendRecord: any): BreedingRecord {
    return {
      recordId: backendRecord.recordId || backendRecord.RecordId,
      eweId: backendRecord.eweId || backendRecord.EweId,
      ramId: backendRecord.ramId || backendRecord.RamId,
      breedingDate: backendRecord.breedingDate || backendRecord.BreedingDate,
      pregnancyDays: backendRecord.pregnancyDays || backendRecord.PregnancyDays,
      success: backendRecord.success || backendRecord.Success,
      ewe: backendRecord.ewe ? this.sheep(backendRecord.ewe) : undefined,
      ram: backendRecord.ram ? this.sheep(backendRecord.ram) : undefined
    }
  },

  // 转换生产记录数据
  productionRecord(backendRecord: any): ProductionRecord {
    return {
      recordId: backendRecord.recordId || backendRecord.RecordId,
      eweId: backendRecord.eweId || backendRecord.EweId,
      produceDate: backendRecord.produceDate || backendRecord.ProduceDate,
      produceCount: backendRecord.produceCount || backendRecord.ProduceCount,
      issues: backendRecord.issues || backendRecord.Issues,
      ewe: backendRecord.ewe ? this.sheep(backendRecord.ewe) : undefined
    }
  },

  // 转换存栏统计数据
  inventoryStatistics(backendData: any): InventoryStatistics {
    return {
      totalSheep: backendData.totalSheep || backendData.TotalSheep || 0,
      breedStatistics: (backendData.breedStatistics || backendData.BreedStatistics || []).map((item: any) => ({
        breed: item.breed || item.Breed,
        totalCount: item.totalCount || item.TotalCount || 0,
        maleCount: item.maleCount || item.MaleCount || 0,
        femaleCount: item.femaleCount || item.FemaleCount || 0,
        healthyCount: item.healthyCount || item.HealthyCount || 0,
        sickCount: item.sickCount || item.SickCount || 0,
        pregnantCount: item.pregnantCount || item.PregnantCount || 0
      })),
      genderStatistics: (backendData.genderStatistics || backendData.GenderStatistics || []).map((item: any) => ({
        gender: item.gender || item.Gender,
        count: item.count || item.Count || 0
      })),
      ageStatistics: (backendData.ageStatistics || backendData.AgeStatistics || []).map((item: any) => ({
        ageGroup: item.ageGroup || item.AgeGroup,
        count: item.count || item.Count || 0
      })),
      queryPeriod: {
        startDate: backendData.queryPeriod?.startDate || backendData.QueryPeriod?.StartDate,
        endDate: backendData.queryPeriod?.endDate || backendData.QueryPeriod?.EndDate
      }
    }
  },

  // 转换产羔统计数据
  lambingStatistics(backendData: any): LambingStatistics {
    return {
      summary: {
        totalLambingRecords: backendData.summary?.totalLambingRecords || backendData.Summary?.TotalLambingRecords || 0,
        totalLambs: backendData.summary?.totalLambs || backendData.Summary?.TotalLambs || 0,
        averageLambsPerLambing: backendData.summary?.averageLambsPerLambing || backendData.Summary?.AverageLambsPerLambing || 0,
        averageSurvivalRate: backendData.summary?.averageSurvivalRate || backendData.Summary?.AverageSurvivalRate || 0
      },
      monthlyStatistics: (backendData.monthlyStatistics || backendData.MonthlyStatistics || []).map((item: any) => ({
        year: item.year || item.Year,
        month: item.month || item.Month,
        monthName: item.monthName || item.MonthName,
        totalLambing: item.totalLambing || item.TotalLambing || 0,
        totalLambs: item.totalLambs || item.TotalLambs || 0,
        averageLambsPerLambing: item.averageLambsPerLambing || item.AverageLambsPerLambing || 0,
        averageSurvivalRate: item.averageSurvivalRate || item.AverageSurvivalRate || 0
      })),
      breedStatistics: (backendData.breedStatistics || backendData.BreedStatistics || []).map((item: any) => ({
        breed: item.breed || item.Breed,
        totalLambing: item.totalLambing || item.TotalLambing || 0,
        totalLambs: item.totalLambs || item.TotalLambs || 0,
        averageLambsPerLambing: item.averageLambsPerLambing || item.AverageLambsPerLambing || 0,
        averageSurvivalRate: item.averageSurvivalRate || item.AverageSurvivalRate || 0
      })),
      topEweStatistics: (backendData.topEweStatistics || backendData.TopEweStatistics || []).map((item: any) => ({
        eweId: item.eweId || item.EweId,
        eweBreed: item.eweBreed || item.EweBreed,
        totalLambing: item.totalLambing || item.TotalLambing || 0,
        totalLambs: item.totalLambs || item.TotalLambs || 0,
        averageLambsPerLambing: item.averageLambsPerLambing || item.AverageLambsPerLambing || 0,
        lastLambingDate: item.lastLambingDate || item.LastLambingDate
      })),
      queryParameters: {
        farmId: backendData.queryParameters?.farmId || backendData.QueryParameters?.FarmId,
        startDate: backendData.queryParameters?.startDate || backendData.QueryParameters?.StartDate,
        endDate: backendData.queryParameters?.endDate || backendData.QueryParameters?.EndDate,
        breed: backendData.queryParameters?.breed || backendData.QueryParameters?.Breed
      }
    }
  },

  // 转换配种统计数据
  breedingStatistics(backendData: any): BreedingStatistics {
    return {
      summary: {
        totalBreedingRecords: backendData.summary?.totalBreedingRecords || backendData.Summary?.TotalBreedingRecords || 0,
        successCount: backendData.summary?.successCount || backendData.Summary?.SuccessCount || 0,
        successRate: backendData.summary?.successRate || backendData.Summary?.SuccessRate || 0,
        averagePregnancyDays: backendData.summary?.averagePregnancyDays || backendData.Summary?.AveragePregnancyDays || 0
      },
      monthlyStatistics: (backendData.monthlyStatistics || backendData.MonthlyStatistics || []).map((item: any) => ({
        year: item.year || item.Year,
        month: item.month || item.Month,
        totalBreeding: item.totalBreeding || item.TotalBreeding || 0,
        successCount: item.successCount || item.SuccessCount || 0,
        successRate: item.successRate || item.SuccessRate || 0
      })),
      breedStatistics: (backendData.breedStatistics || backendData.BreedStatistics || []).map((item: any) => ({
        breed: item.breed || item.Breed,
        totalBreeding: item.totalBreeding || item.TotalBreeding || 0,
        successCount: item.successCount || item.SuccessCount || 0,
        successRate: item.successRate || item.SuccessRate || 0
      })),
      topRamStatistics: (backendData.topRamStatistics || backendData.TopRamStatistics || []).map((item: any) => ({
        ramId: item.ramId || item.RamId,
        ramBreed: item.ramBreed || item.RamBreed,
        totalBreeding: item.totalBreeding || item.TotalBreeding || 0,
        successCount: item.successCount || item.SuccessCount || 0,
        successRate: item.successRate || item.SuccessRate || 0,
        lastBreedingDate: item.lastBreedingDate || item.LastBreedingDate
      })),
      topEweStatistics: (backendData.topEweStatistics || backendData.TopEweStatistics || []).map((item: any) => ({
        eweId: item.eweId || item.EweId,
        eweBreed: item.eweBreed || item.EweBreed,
        totalBreeding: item.totalBreeding || item.TotalBreeding || 0,
        successCount: item.successCount || item.SuccessCount || 0,
        successRate: item.successRate || item.SuccessRate || 0,
        lastBreedingDate: item.lastBreedingDate || item.LastBreedingDate
      })),
      queryParameters: {
        farmId: backendData.queryParameters?.farmId || backendData.QueryParameters?.FarmId,
        startDate: backendData.queryParameters?.startDate || backendData.QueryParameters?.StartDate,
        endDate: backendData.queryParameters?.endDate || backendData.QueryParameters?.EndDate,
        breed: backendData.queryParameters?.breed || backendData.QueryParameters?.Breed
      }
    }
  },

  // 转换生产统计数据
  productionStatistics(backendData: any): ProductionStatistics {
    return {
      summary: {
        totalProductionRecords: backendData.summary?.totalProductionRecords || backendData.Summary?.TotalProductionRecords || 0,
        totalBreedingRecords: backendData.summary?.totalBreedingRecords || backendData.Summary?.TotalBreedingRecords || 0,
        totalAbnormalRecords: backendData.summary?.totalAbnormalRecords || backendData.Summary?.TotalAbnormalRecords || 0,
        totalFarrowingRecords: backendData.summary?.totalFarrowingRecords || backendData.Summary?.TotalFarrowingRecords || 0,
        totalWeaningRecords: backendData.summary?.totalWeaningRecords || backendData.Summary?.TotalWeaningRecords || 0,
        totalFemaleSheep: backendData.summary?.totalFemaleSheep || backendData.Summary?.TotalFemaleSheep || 0,
        totalLambs: backendData.summary?.totalLambs || backendData.Summary?.TotalLambs || 0
      },
      monthlyStatistics: (backendData.monthlyStatistics || backendData.MonthlyStatistics || []).map((item: any) => ({
        year: item.year || item.Year,
        month: item.month || item.Month,
        totalProduction: item.totalProduction || item.TotalProduction || 0,
        averageWeight: item.averageWeight || item.AverageWeight || 0
      })),
      distributionStatistics: {
        total: backendData.distributionStatistics?.total || backendData.DistributionStatistics?.Total || 0,
        departure: backendData.distributionStatistics?.departure || backendData.DistributionStatistics?.Departure || 0,
        liveLambs: backendData.distributionStatistics?.liveLambs || backendData.DistributionStatistics?.LiveLambs || 0,
        deadLambs: backendData.distributionStatistics?.deadLambs || backendData.DistributionStatistics?.DeadLambs || 0
      },
      weaningStatistics: {
        total: backendData.weaningStatistics?.total || backendData.WeaningStatistics?.Total || 0,
        departure: backendData.weaningStatistics?.departure || backendData.WeaningStatistics?.Departure || 0,
        deadLambs: backendData.weaningStatistics?.deadLambs || backendData.WeaningStatistics?.DeadLambs || 0,
        totalWeight: backendData.weaningStatistics?.totalWeight || backendData.WeaningStatistics?.TotalWeight || 0,
        averageWeight: backendData.weaningStatistics?.averageWeight || backendData.WeaningStatistics?.AverageWeight || 0
      },
      productionTableData: (backendData.productionTableData || backendData.ProductionTableData || []).map((item: any) => ({
        category: item.category || item.Category,
        reserveBreeding: item.reserveBreeding || item.ReserveBreeding || 0,
        weaningBreeding: item.weaningBreeding || item.WeaningBreeding || 0,
        returnBreeding: item.returnBreeding || item.ReturnBreeding || 0,
        unbred: item.unbred || item.Unbred || 0,
        emptyBreeding: item.emptyBreeding || item.EmptyBreeding || 0,
        abortionBreeding: item.abortionBreeding || item.AbortionBreeding || 0,
        otherBreeding: item.otherBreeding || item.OtherBreeding || 0,
        totalBreeding: item.totalBreeding || item.TotalBreeding || 0
      }))
    }
  },

  // 转换实时存栏数据
  realTimeInventory(backendData: any[]): RealTimeInventory[] {
    return backendData.map((item: any) => ({
      sheepId: item.sheepId || item.SheepId,
      breed: item.breed || item.Breed,
      gender: item.gender || item.Gender,
      health: item.health || item.Health,
      keeperId: item.keeperId || item.KeeperId,
      location: item.location || item.Location,
      age: item.age || item.Age,
      lastUpdateTime: item.lastUpdateTime || item.LastUpdateTime
    }))
  }
}

/**
 * 数据格式化工具
 */
export const formatData = {
  // 格式化性别显示
  gender(genderCode: string): string {
    return GenderDisplayMap[genderCode] || '未知'
  },

  // 格式化健康状态显示
  health(healthCode: string): string {
    return HealthDisplayMap[healthCode] || '未知'
  },

  // 格式化日期
  date(dateString: string | Date): string {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
  },

  // 格式化日期时间
  dateTime(dateString: string | Date): string {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  },

  // 格式化百分比
  percentage(value: number): string {
    return `${(value * 100).toFixed(1)}%`
  },

  // 格式化数字
  number(value: number): string {
    return value.toLocaleString('zh-CN')
  },

  // 获取品种颜色
  breedColor(breed: string): string {
    return BreedColorMap[breed] || '#95a5a6'
  }
}

/**
 * 数据验证工具
 */
export const validateData = {
  // 验证羊只ID格式
  sheepId(sheepId: string): boolean {
    return /^[A-Z]\d{3}$/.test(sheepId)
  },

  // 验证日期格式
  date(dateString: string): boolean {
    const date = new Date(dateString)
    return !isNaN(date.getTime())
  },

  // 验证数量范围
  count(count: number, min: number = 0, max: number = 100): boolean {
    return count >= min && count <= max
  },

  // 验证存活率范围
  survivalRate(rate: number): boolean {
    return rate >= 0 && rate <= 1
  },

  // 验证怀孕天数范围
  pregnancyDays(days: number): boolean {
    return days >= 140 && days <= 160
  }
}

/**
 * 数据计算工具
 */
export const calculateData = {
  // 计算年龄
  age(birthDate: string | Date): number {
    if (!birthDate) return 0
    const birth = new Date(birthDate)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - birth.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return Math.floor(diffDays / 365)
  },

  // 计算平均存活率
  averageSurvivalRate(records: any[]): number {
    if (records.length === 0) return 0
    const totalRate = records.reduce((sum, record) => sum + (record.survivalRate || 0), 0)
    return totalRate / records.length
  },

  // 计算成功率
  successRate(total: number, success: number): number {
    if (total === 0) return 0
    return (success / total) * 100
  },

  // 计算增长率
  growthRate(current: number, previous: number): number {
    if (previous === 0) return 0
    return ((current - previous) / previous) * 100
  }
} 
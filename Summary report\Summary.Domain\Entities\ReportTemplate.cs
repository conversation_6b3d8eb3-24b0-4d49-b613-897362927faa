using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Summary.Domain.Entities
{
    /// <summary>
    /// 报表模板表
    /// </summary>
    [Table("report_templates")]
    public class ReportTemplate
    {
        /// <summary>
        /// 报表ID - 主键，唯一标识每个报表模板
        /// </summary>
        [Key]
        [Column("report_id")]
        public string ReportId { get; set; } = string.Empty;

        /// <summary>
        /// 筛选条件JSON - 存储报表的筛选条件，如时间范围、品种等
        /// </summary>
        [Column("filter_json")]
        public string? FilterJson { get; set; }

        /// <summary>
        /// 数据快照 - 存储报表生成时的数据快照，用于历史记录
        /// </summary>
        [Column("data_snapshot")]
        public string? DataSnapshot { get; set; }

        /// <summary>
        /// 导出格式 - 报表的导出格式，如：Excel、PDF、Word
        /// </summary>
        [Column("export_format")]
        public string ExportFormat { get; set; } = string.Empty;
    }
} 
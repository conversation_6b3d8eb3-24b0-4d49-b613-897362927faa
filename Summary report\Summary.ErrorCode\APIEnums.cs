﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Summary.ErrorCode
{
    /// <summary>
    /// API返回码枚举 - 用于标识API操作的结果状态
    /// </summary>
    public enum ResultCode
    {
        /// <summary>
        /// 成功 - 操作执行成功，返回200状态码
        /// </summary>
        [Description("成功")]
        Success = 200,
        
        /// <summary>
        /// 失败 - 操作执行失败，返回500状态码
        /// </summary>
        [Description("失败")]
        Fail = 500,
        
        /// <summary>
        /// 未找到 - 请求的资源不存在，返回404状态码
        /// </summary>
        [Description("未找到")]
        NotFound = 404,
        
        // 以下是预留的扩展返回码，可根据需要启用
        //[Description("登录成功")]
        //LoginSuccess = 100,
        //[Description("登录失败")]
        //LoginFail = 101,
        //[Description("查询成功")]
        //SearchSuccess = 200,
        //[Description("查询失败")]
        //SearchFail = 201,
        //[Description("添加成功")]
        //AddSuccess = 300,
        //[Description("添加失败")]
        //AddFail = 301,
        //[Description("更新成功")]
        //UpdateSuccess = 400,
        //[Description("更新失败")]
        //UpdateFail = 401,
        //[Description("删除成功")]
        //DeleteSuccess = 500,
        //[Description("删除失败")]
        //DeleteFail = 501,
    }
}

<template>
  <div class="inventory-view">
    <!-- 返回首页按钮 -->
    <BackToHome />
    
    <!-- 查询条件区域 -->
    <div class="dashboard-card query-section">
      <el-form :model="queryForm" inline>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :default-value="[new Date('2023-04-01'), new Date('2023-04-01')]"
          />
        </el-form-item>
        <el-form-item label="汇总方式">
          <el-select v-model="queryForm.summaryType" placeholder="请选择汇总方式">
            <el-option label="按栋舍" value="building" />
            <el-option label="按品种" value="breed" />
            <el-option label="按类型" value="type" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="apiStore.loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="showAdvancedQuery = !showAdvancedQuery">
          <el-icon><Filter /></el-icon>
          高级查询
        </el-button>
        <el-button @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 高级查询面板 -->
    <el-collapse-transition>
      <div v-show="showAdvancedQuery" class="dashboard-card advanced-query">
        <h4>查询条件:</h4>
        <el-form :model="advancedQueryForm" inline>
          <el-form-item label="日期">
            <el-date-picker
              v-model="advancedQueryForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="羊只类型:">
            <el-select v-model="advancedQueryForm.sheepType" placeholder="请选择羊只类型">
              <el-option label="种公" value="male" />
              <el-option label="种母" value="female" />
              <el-option label="后裔" value="offspring" />
            </el-select>
          </el-form-item>
          <el-form-item label="查询条件">
            <el-select v-model="advancedQueryForm.queryCondition" placeholder="请选择查询条件">
              <el-option label="栋舍" value="building" />
              <el-option label="耳号或批次" value="earTag" />
              <el-option label="品种" value="breed" />
            </el-select>
          </el-form-item>
          <el-form-item label="查询条件值">
            <el-input v-model="advancedQueryForm.queryValue" placeholder="请输入查询值" />
          </el-form-item>
          <el-form-item label="汇总类型:">
            <el-select v-model="advancedQueryForm.summaryType" placeholder="请选择汇总类型">
              <el-option label="栋舍类型" value="buildingType" />
              <el-option label="栋舍" value="building" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleAdvancedQuery">查询</el-button>
            <el-button @click="showAdvancedQuery = false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>

    <!-- 存栏统计表格 -->
    <div class="dashboard-card table-section">
      <div class="table-header">
        <h3>羊场存栏变动统计表</h3>
        <div class="table-actions">
          <el-button size="small" @click="refreshTable">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f8f9fa', color: '#303133' }"
      >
        <el-table-column prop="category" label="类别" width="120" fixed="left" />
        <el-table-column prop="beginning" label="期初" width="80" />
        
        <!-- 转入相关列 -->
        <el-table-column label="转入" align="center">
          <el-table-column prop="transferIn.pen" label="转舍转入" width="80" />
          <el-table-column prop="transferIn.purchase" label="新购转入" width="80" />
          <el-table-column prop="transferIn.breeding" label="配种转入" width="80" />
          <el-table-column prop="transferIn.pregnancy" label="妊娠转入" width="80" />
          <el-table-column prop="transferIn.farrowing" label="分娩转入" width="80" />
          <el-table-column prop="transferIn.allocation" label="公平调拨转入" width="100" />
          <el-table-column prop="transferIn.freeRange" label="散放转入" width="80" />
          <el-table-column prop="transferIn.breedingSheep" label="种羊转入" width="80" />
          <el-table-column prop="transferIn.allocationIn" label="调拨转入" width="80" />
          <el-table-column prop="transferIn.inventory" label="盘点转入" width="80" />
          <el-table-column prop="transferIn.recovery" label="回收转入" width="80" />
          <el-table-column prop="transferIn.birth" label="出生" width="80" />
          <el-table-column prop="transferIn.purchaseIn" label="采购转入" width="80" />
          <el-table-column prop="transferIn.foster" label="寄养转入" width="80" />
          <el-table-column prop="transferIn.tempReturn" label="暂估退回" width="80" />
          <el-table-column prop="transferIn.subtotal" label="转入小计" width="80" />
        </el-table-column>
        
        <!-- 转出相关列 -->
        <el-table-column label="转出" align="center">
          <el-table-column prop="transferOut.pen" label="转舍转出" width="80" />
          <el-table-column prop="transferOut.reserve" label="后备转出" width="80" />
          <el-table-column prop="transferOut.death" label="死亡转出" width="80" />
          <el-table-column prop="transferOut.culling" label="淘汰转出" width="80" />
          <el-table-column prop="transferOut.sales" label="销售转出" width="80" />
          <el-table-column prop="transferOut.allocation" label="调拨转出" width="80" />
          <el-table-column prop="transferOut.inventory" label="盘点转出" width="80" />
          <el-table-column prop="transferOut.sale" label="出售转出" width="80" />
          <el-table-column prop="transferOut.foster" label="寄养转出" width="80" />
          <el-table-column prop="transferOut.subtotal" label="转出小计" width="80" />
        </el-table-column>
        
        <el-table-column prop="ending" label="期末" width="80" />
      </el-table>
    </div>

    <!-- 统计图表 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3>种母存栏变动</h3>
          </div>
          <div class="chart-container" ref="femaleChangeChartRef"></div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3>羔羊存栏</h3>
          </div>
          <div class="chart-container" ref="lambInventoryChartRef"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useApiStore } from '@/stores/api'
import { 
  Search, 
  Filter, 
  Printer, 
  Download, 
  Refresh 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import BackToHome from '@/components/BackToHome.vue'

const apiStore = useApiStore()

// 响应式数据
const showAdvancedQuery = ref(false)
const queryForm = reactive({
  dateRange: ['2023-04-01', '2023-04-01'],
  summaryType: 'building'
})

const advancedQueryForm = reactive({
  dateRange: ['2023-04-01', '2023-04-01'],
  sheepType: '',
  queryCondition: '',
  queryValue: '',
  summaryType: ''
})

// 表格数据
const tableData = ref([
  {
    category: '种公存栏',
    beginning: 18,
    transferIn: {
      pen: 0, purchase: 0, breeding: 0, pregnancy: 0, farrowing: 0,
      allocation: 0, freeRange: 0, breedingSheep: 0, allocationIn: 0,
      inventory: 0, recovery: 0, birth: 0, purchaseIn: 0, foster: 0,
      tempReturn: 0, subtotal: 0
    },
    transferOut: {
      pen: 0, reserve: 0, death: 0, culling: 0, sales: 0,
      allocation: 0, inventory: 0, sale: 0, foster: 0, subtotal: 0
    },
    ending: 18
  },
  {
    category: '种母存栏',
    beginning: 3,
    transferIn: {
      pen: 0, purchase: 0, breeding: 0, pregnancy: 0, farrowing: 0,
      allocation: 0, freeRange: 0, breedingSheep: 0, allocationIn: 0,
      inventory: 0, recovery: 0, birth: 0, purchaseIn: 0, foster: 0,
      tempReturn: 0, subtotal: 0
    },
    transferOut: {
      pen: 0, reserve: 0, death: 0, culling: 0, sales: 0,
      allocation: 0, inventory: 0, sale: 0, foster: 0, subtotal: 0
    },
    ending: 3
  },
  {
    category: '羔羊存栏',
    beginning: 115,
    transferIn: {
      pen: 0, purchase: 0, breeding: 0, pregnancy: 0, farrowing: 0,
      allocation: 0, freeRange: 0, breedingSheep: 0, allocationIn: 0,
      inventory: 0, recovery: 0, birth: 0, purchaseIn: 0, foster: 0,
      tempReturn: 0, subtotal: 0
    },
    transferOut: {
      pen: 0, reserve: 0, death: 0, culling: 0, sales: 0,
      allocation: 0, inventory: 0, sale: 0, foster: 0, subtotal: 0
    },
    ending: 115
  }
])

// 图表引用
const femaleChangeChartRef = ref<HTMLElement>()
const lambInventoryChartRef = ref<HTMLElement>()

// 查询处理
const handleQuery = async () => {
  try {
    const params = {
      startDate: queryForm.dateRange[0],
      endDate: queryForm.dateRange[1],
      summaryType: queryForm.summaryType
    }
    
    const response = await apiStore.getInventoryStatistics(params)
    if (response && response.data) {
      // 更新表格数据
      updateTableData(response.data)
    }
  } catch (error) {
    console.error('查询失败:', error)
  }
}

// 高级查询处理
const handleAdvancedQuery = async () => {
  try {
    const params = {
      startDate: advancedQueryForm.dateRange[0],
      endDate: advancedQueryForm.dateRange[1],
      sheepType: advancedQueryForm.sheepType,
      queryCondition: advancedQueryForm.queryCondition,
      queryValue: advancedQueryForm.queryValue,
      summaryType: advancedQueryForm.summaryType
    }
    
    const response = await apiStore.getInventoryStatistics(params)
    if (response && response.data) {
      updateTableData(response.data)
    }
  } catch (error) {
    console.error('高级查询失败:', error)
  }
}

// 更新表格数据
const updateTableData = (data: any) => {
  // 根据后端返回的数据更新表格
  if (data) {
    // 构建存栏变动统计表格数据
    const newTableData = []
    
    // 种公存栏
    const maleStats = data.GenderStatistics?.find((item: any) => item.Gender === '公羊')
    if (maleStats) {
      newTableData.push({
        category: '种公存栏',
        beginning: maleStats.Count || 0,
        transferIn: {
          pen: 0, purchase: 0, breeding: 0, pregnancy: 0, farrowing: 0,
          allocation: 0, freeRange: 0, breedingSheep: 0, allocationIn: 0,
          inventory: 0, recovery: 0, birth: 0, purchaseIn: 0, foster: 0,
          tempReturn: 0, subtotal: 0
        },
        transferOut: {
          pen: 0, reserve: 0, death: 0, culling: 0, sales: 0,
          allocation: 0, inventory: 0, sale: 0, foster: 0, subtotal: 0
        },
        ending: maleStats.Count || 0
      })
    }
    
    // 种母存栏
    const femaleStats = data.GenderStatistics?.find((item: any) => item.Gender === '母羊')
    if (femaleStats) {
      newTableData.push({
        category: '种母存栏',
        beginning: femaleStats.Count || 0,
        transferIn: {
          pen: 0, purchase: 0, breeding: 0, pregnancy: 0, farrowing: 0,
          allocation: 0, freeRange: 0, breedingSheep: 0, allocationIn: 0,
          inventory: 0, recovery: 0, birth: 0, purchaseIn: 0, foster: 0,
          tempReturn: 0, subtotal: 0
        },
        transferOut: {
          pen: 0, reserve: 0, death: 0, culling: 0, sales: 0,
          allocation: 0, inventory: 0, sale: 0, foster: 0, subtotal: 0
        },
        ending: femaleStats.Count || 0
      })
    }
    
    // 羔羊存栏
    const ageStats = data.AgeStatistics || []
    const lambCount = ageStats
      .filter((item: any) => item.AgeGroup === '0-12个月' || item.AgeGroup === '幼羊')
      .reduce((sum: number, item: any) => sum + (item.Count || 0), 0)
    
    newTableData.push({
      category: '羔羊存栏',
      beginning: lambCount,
      transferIn: {
        pen: 0, purchase: 0, breeding: 0, pregnancy: 0, farrowing: 0,
        allocation: 0, freeRange: 0, breedingSheep: 0, allocationIn: 0,
        inventory: 0, recovery: 0, birth: 0, purchaseIn: 0, foster: 0,
        tempReturn: 0, subtotal: 0
      },
      transferOut: {
        pen: 0, reserve: 0, death: 0, culling: 0, sales: 0,
        allocation: 0, inventory: 0, sale: 0, foster: 0, subtotal: 0
      },
      ending: lambCount
    })
    
    tableData.value = newTableData
  }
}

// 刷新表格
const refreshTable = () => {
  handleQuery()
}

// 打印功能
const handlePrint = () => {
  ElMessage.info('打印功能开发中...')
}

// 导出功能
const handleExport = async () => {
  try {
    const params = {
      startDate: queryForm.dateRange[0],
      endDate: queryForm.dateRange[1],
      format: 'Excel'
    }
    
    await apiStore.generateReport('inventory', params)
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 初始化种母存栏变动图表
const initFemaleChangeChart = () => {
  if (!femaleChangeChartRef.value) return
  
  const chart = echarts.init(femaleChangeChartRef.value)
  const option = {
    title: {
      text: '种母存栏变动',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['期初', '转入', '转出', '期末'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['后备转种母', '采购', '调拨转入', '死亡', '淘汰', '销售', '调拨转出', '其他']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '期初',
        type: 'bar',
        data: [3, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '转入',
        type: 'bar',
        data: [0, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: { color: '#5470c6' }
      },
      {
        name: '转出',
        type: 'bar',
        data: [0, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: { color: '#ee6666' }
      },
      {
        name: '期末',
        type: 'bar',
        data: [3, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: { color: '#fac858' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化羔羊存栏图表
const initLambInventoryChart = () => {
  if (!lambInventoryChartRef.value) return
  
  const chart = echarts.init(lambInventoryChartRef.value)
  const option = {
    title: {
      text: '羔羊存栏',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['期初', '转入', '转出', '期末'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['产羔', '采购', '调拨转入', '盘点转入', '回收转入', '后备转入', '死亡', '销售', '调拨转出', '盘点转出', '出前转出', '其他']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '期初',
        type: 'bar',
        data: [115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '转入',
        type: 'bar',
        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: { color: '#5470c6' }
      },
      {
        name: '转出',
        type: 'bar',
        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: { color: '#ee6666' }
      },
      {
        name: '期末',
        type: 'bar',
        data: [115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: { color: '#fac858' }
      }
    ]
  }
  chart.setOption(option)
}

onMounted(async () => {
  await handleQuery()
  
  await nextTick()
  initFemaleChangeChart()
  initLambInventoryChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    const femaleChart = echarts.getInstanceByDom(femaleChangeChartRef.value!)
    const lambChart = echarts.getInstanceByDom(lambInventoryChartRef.value!)
    femaleChart?.resize()
    lambChart?.resize()
  })
})
</script>

<style scoped>
.inventory-view {
  padding: 20px 0;
}

.query-section {
  margin-bottom: 20px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.advanced-query {
  margin-bottom: 20px;
  padding: 20px;
}

.advanced-query h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.table-section {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.table-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.charts-section {
  margin-bottom: 20px;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 300px;
  padding: 20px;
}

@media (max-width: 768px) {
  .query-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .table-header {
    flex-direction: column;
    gap: 10px;
  }
}
</style> 
{"format": 1, "restore": {"D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.API.Write\\Summary.API.Write.csproj": {}}, "projects": {"D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.API.Write\\Summary.API.Write.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.API.Write\\Summary.API.Write.csproj", "projectName": "Summary.API.Write", "projectPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.API.Write\\Summary.API.Write.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.API.Write\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Domain\\Summary.Domain.csproj": {"projectPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Domain\\Summary.Domain.csproj"}, "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.ErrorCode\\Summary.ErrorCode.csproj": {"projectPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.ErrorCode\\Summary.ErrorCode.csproj"}, "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Infrastructure\\Summary.Infrastructure.csproj": {"projectPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Infrastructure\\Summary.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "IGeekFan.AspNetCore.Knife4jUI": {"target": "Package", "version": "[0.0.16, )"}, "MediatR": {"target": "Package", "version": "[13.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "NLog.Config": {"target": "Package", "version": "[4.7.15, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Domain\\Summary.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Domain\\Summary.Domain.csproj", "projectName": "Summary.Domain", "projectPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Domain\\Summary.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.ErrorCode\\Summary.ErrorCode.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.ErrorCode\\Summary.ErrorCode.csproj", "projectName": "Summary.ErrorCode", "projectPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.ErrorCode\\Summary.ErrorCode.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.ErrorCode\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Infrastructure\\Summary.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Infrastructure\\Summary.Infrastructure.csproj", "projectName": "Summary.Infrastructure", "projectPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Infrastructure\\Summary.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Domain\\Summary.Domain.csproj": {"projectPath": "D:\\实训一\\项目\\农业\\Summary\\Summary report\\Summary.Domain\\Summary.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}}}
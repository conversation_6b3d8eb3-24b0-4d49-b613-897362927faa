using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Read.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.Handler
{
    /// <summary>
    /// 羊场实时存栏处理器
    /// </summary>
    public class RealTimeInventoryHandler : 
        IRequestHandler<GetRealTimeInventoryCommand, APIResult<object>>,
        IRequestHandler<GetSheepLocationMapCommand, APIResult<object>>,
        IRequestHandler<GetSheepDetailsCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public RealTimeInventoryHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取羊场实时存栏
        /// </summary>
        public async Task<APIResult<object>> Handle(GetRealTimeInventoryCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var sheepList = await _context.Sheep
                    .Where(s => s.FarmId == request.FarmId)
                    .ToListAsync(cancellationToken);

                object data;
                if (request.DisplayMode?.ToLower() == "map")
                {
                    // 地图模式：返回位置信息
                    data = sheepList.Select(s => new
                    {
                        s.SheepId,
                        s.Breed,
                        s.Gender,
                        s.Health,
                        s.KeeperId,
                        s.Location,
                        Position = ParseLocation(s.Location)
                    }).ToList();
                }
                else
                {
                    // 列表模式：返回详细信息
                    data = sheepList.Select(s => new
                    {
                        s.SheepId,
                        s.Breed,
                        Gender = s.Gender == "1" ? "公羊" : "母羊",
                        s.Health,
                        s.KeeperId,
                        s.Location,
                        Age = CalculateAge(s.BirthDate),
                        LastUpdateTime = DateTime.Now // 模拟实时更新时间
                    }).ToList();
                }

                result.Code = ResultCode.Success;
                result.Message = "获取羊场实时存栏成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取羊场实时存栏失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 获取羊只位置地图
        /// </summary>
        public async Task<APIResult<object>> Handle(GetSheepLocationMapCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var sheepWithLocation = await _context.Sheep
                    .Where(s => s.FarmId == request.FarmId && !string.IsNullOrEmpty(s.Location))
                    .Select(s => new
                    {
                        s.SheepId,
                        s.Breed,
                        s.Gender,
                        s.Health,
                        s.KeeperId,
                        s.Location,
                        Position = ParseLocation(s.Location)
                    })
                    .ToListAsync(cancellationToken);

                var data = new
                {
                    FarmId = request.FarmId,
                    TotalSheepWithLocation = sheepWithLocation.Count,
                    SheepLocations = sheepWithLocation,
                    MapCenter = CalculateMapCenter(sheepWithLocation.Cast<object>().ToList())
                };

                result.Code = ResultCode.Success;
                result.Message = "获取羊只位置地图成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取羊只位置地图失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 获取羊只详情
        /// </summary>
        public async Task<APIResult<object>> Handle(GetSheepDetailsCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var sheep = await _context.Sheep
                    .Include(s => s.LambingRecords)
                    .Include(s => s.BreedingRecordsAsEwe)
                    .Include(s => s.BreedingRecordsAsRam)
                    .Include(s => s.ProductionRecords)
                    .FirstOrDefaultAsync(s => s.SheepId == request.SheepId, cancellationToken);

                if (sheep == null)
                {
                    result.Code = ResultCode.NotFound;
                    result.Message = "羊只不存在";
                    return result;
                }

                var data = new
                {
                    BasicInfo = new
                    {
                        sheep.SheepId,
                        sheep.FarmId,
                        sheep.Breed,
                        Gender = sheep.Gender == "1" ? "公羊" : "母羊",
                        sheep.BirthDate,
                        Age = CalculateAge(sheep.BirthDate),
                        sheep.Health,
                        sheep.KeeperId,
                        sheep.Location
                    },
                    Statistics = new
                    {
                        LambingCount = sheep.LambingRecords.Count,
                        BreedingCount = sheep.BreedingRecordsAsEwe.Count,
                        ProductionCount = sheep.ProductionRecords.Count
                    },
                    RecentRecords = new
                    {
                        LastLambing = sheep.LambingRecords.OrderByDescending(l => l.LambingDate).FirstOrDefault(),
                        LastBreeding = sheep.BreedingRecordsAsEwe.OrderByDescending(b => b.BreedingDate).FirstOrDefault(),
                        LastProduction = sheep.ProductionRecords.OrderByDescending(p => p.ProduceDate).FirstOrDefault()
                    }
                };

                result.Code = ResultCode.Success;
                result.Message = "获取羊只详情成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取羊只详情失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 解析位置信息
        /// </summary>
        private object ParseLocation(string? location)
        {
            if (string.IsNullOrEmpty(location))
                return new { Latitude = 0, Longitude = 0 };

            // 假设位置格式为 "纬度,经度"
            var parts = location.Split(',');
            if (parts.Length == 2 && double.TryParse(parts[0], out var lat) && double.TryParse(parts[1], out var lng))
            {
                return new { Latitude = lat, Longitude = lng };
            }

            return new { Latitude = 0, Longitude = 0 };
        }

        /// <summary>
        /// 计算地图中心点
        /// </summary>
        private object CalculateMapCenter(List<object> sheepLocations)
        {
            if (!sheepLocations.Any())
                return new { Latitude = 0, Longitude = 0 };

            // 这里简化处理，实际应该计算所有羊只位置的平均值
            return new { Latitude = 39.9042, Longitude = 116.4074 }; // 默认北京坐标
        }

        /// <summary>
        /// 计算年龄
        /// </summary>
        private int? CalculateAge(DateTime? birthDate)
        {
            if (!birthDate.HasValue)
                return null;

            return DateTime.Now.Year - birthDate.Value.Year;
        }
    }
} 
<template>
  <div class="dashboard-view">
    <!-- 刷新按钮 -->
    <RefreshButton @refresh="handleRefresh" :loading="apiStore.loading" />
    
    <!-- 导航标签页 -->
    <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="羊场首页" name="home">
        <div class="dashboard-content">
          <!-- 顶部统计卡片 -->
          <el-row :gutter="20" class="stats-row">
            <el-col :span="6">
              <div class="dashboard-card stat-card">
                <div class="stat-number">{{ totalInventory }}</div>
                <div class="stat-label">总存栏数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="dashboard-card stat-card">
                <div class="stat-number">{{ femaleSheep }}</div>
                <div class="stat-label">种母存栏</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="dashboard-card stat-card">
                <div class="stat-number">{{ maleSheep }}</div>
                <div class="stat-label">种公存栏</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="dashboard-card stat-card">
                <div class="stat-number">{{ lambs }}</div>
                <div class="stat-label">羔羊存栏</div>
              </div>
            </el-col>
          </el-row>

          <!-- 图表区域 -->
          <el-row :gutter="20" class="charts-row">
            <el-col :span="12">
              <div class="dashboard-card">
                <div class="card-header">
                  <h3>种母存栏结构</h3>
                </div>
                <div class="chart-container" ref="femaleChartRef"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="dashboard-card">
                <div class="card-header">
                  <h3>种公存栏品种结构</h3>
                </div>
                <div class="chart-container" ref="maleChartRef"></div>
              </div>
            </el-col>
          </el-row>

          <!-- 快速操作区域 -->
          <el-row :gutter="20" class="actions-row">
            <el-col :span="24">
              <div class="dashboard-card">
                <div class="card-header">
                  <h3>快速操作</h3>
                </div>
                <div class="quick-actions">
                  <el-button type="primary" @click="navigateTo('/inventory')">
                    <el-icon><DataAnalysis /></el-icon>
                    存栏统计
                  </el-button>
                  <el-button type="success" @click="navigateTo('/lambing')">
                    <el-icon><Calendar /></el-icon>
                    产羔统计
                  </el-button>
                  <el-button type="warning" @click="navigateTo('/breeding')">
                    <el-icon><Connection /></el-icon>
                    配种统计
                  </el-button>
                  <el-button type="info" @click="navigateTo('/production')">
                    <el-icon><TrendCharts /></el-icon>
                    生产统计
                  </el-button>
                  <el-button @click="navigateTo('/records')">
                    <el-icon><Edit /></el-icon>
                    记录管理
                  </el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="羊场存栏统计" name="inventory">
        <InventoryView />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useApiStore } from '@/stores/api'
import { 
  DataAnalysis, 
  Calendar, 
  Connection, 
  TrendCharts, 
  Edit 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import InventoryView from './InventoryView.vue'
import RefreshButton from '@/components/RefreshButton.vue'

const router = useRouter()
const apiStore = useApiStore()

// 响应式数据
const activeTab = ref('home')
const totalInventory = ref(0)
const femaleSheep = ref(0)
const maleSheep = ref(0)
const lambs = ref(0)

// 图表引用
const femaleChartRef = ref<HTMLElement>()
const maleChartRef = ref<HTMLElement>()

// 处理标签页点击
const handleTabClick = (tab: any) => {
  if (tab.props.name === 'inventory') {
    router.push('/inventory')
  }
}

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path)
}

// 处理刷新
const handleRefresh = async () => {
  await loadInventoryData()
}

// 初始化种母存栏图表
const initFemaleChart = (data: any) => {
  if (!femaleChartRef.value) return
  
  const chart = echarts.init(femaleChartRef.value)
  
  // 根据真实数据构建图表数据
  const chartData = data?.GenderStatistics?.find((item: any) => item.Gender === '母羊')?.Count || 0
  const pregnantCount = data?.BreedStatistics?.reduce((sum: number, item: any) => sum + (item.PregnantCount || 0), 0) || 0
  const healthyCount = data?.BreedStatistics?.reduce((sum: number, item: any) => sum + (item.HealthyCount || 0), 0) || 0
  
  const option = {
    title: {
      text: '种母存栏结构',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['妊娠', '健康', '其他']
    },
    series: [
      {
        name: '种母存栏',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: [
          { value: pregnantCount, name: '妊娠', itemStyle: { color: '#ff9f7f' } },
          { value: healthyCount - pregnantCount, name: '健康', itemStyle: { color: '#ff6b6b' } },
          { value: Math.max(0, chartData - healthyCount), name: '其他', itemStyle: { color: '#ee5a52' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化种公存栏图表
const initMaleChart = (data: any) => {
  if (!maleChartRef.value) return
  
  const chart = echarts.init(maleChartRef.value)
  
  // 根据真实数据构建图表数据
  const breedStats = data?.BreedStatistics || []
  const maleBreedData = breedStats.map((item: any) => ({
    value: item.MaleCount || 0,
    name: item.Breed,
    itemStyle: { color: getBreedColor(item.Breed) }
  })).filter((item: any) => item.value > 0)
  
  const option = {
    title: {
      text: '种公存栏品种结构',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}只 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: maleBreedData.map((item: any) => item.name)
    },
    series: [
      {
        name: '种公存栏',
        type: 'pie',
        radius: '50%',
        center: ['60%', '50%'],
        data: maleBreedData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 获取品种颜色
const getBreedColor = (breed: string) => {
  const colorMap: Record<string, string> = {
    '杜泊': '#3498db',
    '澳洲白': '#2ecc71',
    '萨福克': '#f1c40f',
    '杜湖': '#e74c3c',
    '澳湖': '#9b59b6'
  }
  return colorMap[breed] || '#95a5a6'
}

// 加载实时数据
const loadRealTimeData = async () => {
  try {
    const response = await apiStore.getRealTimeInventory()
    if (response && response.data) {
      const data = response.data
      
      // 计算统计数据
      if (Array.isArray(data)) {
        totalInventory.value = data.length
        femaleSheep.value = data.filter((item: any) => item.Gender === '母羊').length
        maleSheep.value = data.filter((item: any) => item.Gender === '公羊').length
        lambs.value = data.filter((item: any) => {
          const age = item.Age || 0
          return age < 12 // 假设12个月以下为羔羊
        }).length
      }
    }
  } catch (error) {
    console.error('加载实时数据失败:', error)
  }
}

// 加载存栏统计数据
const loadInventoryData = async () => {
  try {
    const response = await apiStore.getInventoryStatistics({})
    if (response && response.data) {
      const data = response.data
      
      // 更新统计数据
      totalInventory.value = data.totalSheep || 0
      
      // 计算性别统计
      const genderStats = data.genderStatistics || []
      femaleSheep.value = genderStats.find((item: any) => item.gender === '母羊')?.count || 0
      maleSheep.value = genderStats.find((item: any) => item.gender === '公羊')?.count || 0
      
      // 计算羔羊数量（假设年龄小于12个月的为羔羊）
      const ageStats = data.ageStatistics || []
      lambs.value = ageStats
        .filter((item: any) => item.AgeGroup === '0-12个月' || item.AgeGroup === '幼羊')
        .reduce((sum: number, item: any) => sum + (item.Count || 0), 0)
      
      // 初始化图表
      await nextTick()
      initFemaleChart(data)
      initMaleChart(data)
    }
  } catch (error) {
    console.error('加载存栏统计数据失败:', error)
  }
}

onMounted(async () => {
  await loadInventoryData()
  
  // 监听窗口大小变化，重新调整图表
  window.addEventListener('resize', () => {
    const femaleChart = echarts.getInstanceByDom(femaleChartRef.value!)
    const maleChart = echarts.getInstanceByDom(maleChartRef.value!)
    femaleChart?.resize()
    maleChart?.resize()
  })
})
</script>

<style scoped>
.dashboard-view {
  height: 100%;
}

.dashboard-content {
  padding: 20px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.charts-row {
  margin-bottom: 20px;
}

.actions-row {
  margin-bottom: 20px;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.quick-actions {
  padding: 20px;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  min-width: 120px;
  height: 40px;
}

.chart-container {
  height: 300px;
  padding: 20px;
}

@media (max-width: 768px) {
  .stats-row .el-col {
    margin-bottom: 15px;
  }
  
  .quick-actions {
    justify-content: center;
  }
  
  .quick-actions .el-button {
    min-width: 100px;
  }
}
</style> 
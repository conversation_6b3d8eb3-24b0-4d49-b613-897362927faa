﻿using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;

namespace Summary.Infrastructure
{
    /// <summary>
    /// 数据库上下文
    /// </summary>
    public class MyDbContext : DbContext
    {
        public MyDbContext(DbContextOptions<MyDbContext> options) : base(options)
        {
        }

        // 实体集
        public DbSet<Sheep> Sheep { get; set; }
        public DbSet<LambingRecord> LambingRecords { get; set; }
        public DbSet<BreedingRecord> BreedingRecords { get; set; }
        public DbSet<ProductionRecord> ProductionRecords { get; set; }
        public DbSet<ReportTemplate> ReportTemplates { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置Sheep实体
            modelBuilder.Entity<Sheep>(entity =>
            {
                entity.HasKey(e => e.SheepId);
                
                // 创建索引以提高查询性能
                entity.HasIndex(e => e.FarmId);
                entity.HasIndex(e => e.Breed);
                entity.HasIndex(e => e.Gender);
                entity.HasIndex(e => e.KeeperId);
            });

            // 配置LambingRecord实体
            modelBuilder.Entity<LambingRecord>(entity =>
            {
                entity.HasKey(e => e.RecordId);
                
                // 外键关系
                entity.HasOne(e => e.Ewe)
                      .WithMany(s => s.LambingRecords)
                      .HasForeignKey(e => e.EweId)
                      .OnDelete(DeleteBehavior.Cascade);
                
                // 创建索引
                entity.HasIndex(e => e.EweId);
                entity.HasIndex(e => e.LambingDate);
            });

            // 配置BreedingRecord实体
            modelBuilder.Entity<BreedingRecord>(entity =>
            {
                entity.HasKey(e => e.RecordId);
                
                // 外键关系
                entity.HasOne(e => e.Ewe)
                      .WithMany(s => s.BreedingRecordsAsEwe)
                      .HasForeignKey(e => e.EweId)
                      .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Ram)
                      .WithMany(s => s.BreedingRecordsAsRam)
                      .HasForeignKey(e => e.RamId)
                      .OnDelete(DeleteBehavior.Cascade);
                
                // 创建索引
                entity.HasIndex(e => e.EweId);
                entity.HasIndex(e => e.RamId);
                entity.HasIndex(e => e.BreedingDate);
            });

            // 配置ProductionRecord实体
            modelBuilder.Entity<ProductionRecord>(entity =>
            {
                entity.HasKey(e => e.RecordId);
                
                // 外键关系
                entity.HasOne(e => e.Ewe)
                      .WithMany(s => s.ProductionRecords)
                      .HasForeignKey(e => e.EweId)
                      .OnDelete(DeleteBehavior.Cascade);
                
                // 创建索引
                entity.HasIndex(e => e.EweId);
                entity.HasIndex(e => e.ProduceDate);
            });

            // 配置ReportTemplate实体
            modelBuilder.Entity<ReportTemplate>(entity =>
            {
                entity.HasKey(e => e.ReportId);
            });
        }
    }
}

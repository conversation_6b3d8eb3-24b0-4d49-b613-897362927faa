using MediatR;
using Summary.Domain.Entities;
using Summary.ErrorCode;

namespace Summary.API.Write.Application.command
{
    /// <summary>
    /// 添加配种记录命令
    /// </summary>
    public class AddBreedingRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 母羊ID
        /// </summary>
        public string EweId { get; set; } = string.Empty;
        
        /// <summary>
        /// 公羊ID
        /// </summary>
        public string RamId { get; set; } = string.Empty;
        
        /// <summary>
        /// 配种日期
        /// </summary>
        public DateTime BreedingDate { get; set; }
        
        /// <summary>
        /// 妊娠天数
        /// </summary>
        public int? PregnancyDays { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 更新配种记录命令
    /// </summary>
    public class UpdateBreedingRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long RecordId { get; set; }
        
        /// <summary>
        /// 配种日期
        /// </summary>
        public DateTime? BreedingDate { get; set; }
        
        /// <summary>
        /// 妊娠天数
        /// </summary>
        public int? PregnancyDays { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool? Success { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 删除配种记录命令
    /// </summary>
    public class DeleteBreedingRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long RecordId { get; set; }
    }
} 
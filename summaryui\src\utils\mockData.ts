// 模拟数据生成器 - 完全匹配后端数据结构
import type {
  InventoryStatistics,
  LambingStatistics,
  BreedingStatistics,
  ProductionStatistics,
  RealTimeInventory,
  BreedStatistics,
  GenderStatistics,
  AgeStatistics,
  MonthlyLambingStatistics,
  BreedLambingStatistics,
  TopEweStatistics,
  MonthlyBreedingStatistics,
  BreedBreedingStatistics,
  TopRamStatistics,
  TopBreedingEweStatistics,
  MonthlyProductionStatistics,
  DistributionStatistics,
  WeaningStatistics,
  ProductionTableData
} from '@/types'

export const generateMockData = {
  // 生成存栏统计数据
  inventoryStatistics(): InventoryStatistics {
    return {
      totalSheep: 136,
      breedStatistics: [
        { breed: '杜泊', totalCount: 45, maleCount: 18, femaleCount: 27, healthyCount: 42, sickCount: 2, pregnantCount: 8 },
        { breed: '澳洲白', totalCount: 38, maleCount: 12, femaleCount: 26, healthyCount: 35, sickCount: 2, pregnantCount: 6 },
        { breed: '萨福克', totalCount: 28, maleCount: 8, femaleCount: 20, healthyCount: 26, sickCount: 1, pregnantCount: 4 },
        { breed: '杜湖', totalCount: 15, maleCount: 5, femaleCount: 10, healthyCount: 14, sickCount: 0, pregnantCount: 2 },
        { breed: '澳湖', totalCount: 10, maleCount: 3, femaleCount: 7, healthyCount: 9, sickCount: 0, pregnantCount: 1 }
      ],
      genderStatistics: [
        { gender: '公羊', count: 46 },
        { gender: '母羊', count: 90 }
      ],
      ageStatistics: [
        { ageGroup: '0-12个月', count: 85 },
        { ageGroup: '1-2岁', count: 35 },
        { ageGroup: '2-3岁', count: 12 },
        { ageGroup: '3岁以上', count: 4 }
      ],
      queryPeriod: {
        startDate: undefined,
        endDate: undefined
      }
    }
  },

  // 生成产羔统计数据
  lambingStatistics(): LambingStatistics {
    return {
      summary: {
        totalLambingRecords: 156,
        totalLambs: 312,
        averageLambsPerLambing: 2.0,
        averageSurvivalRate: 95.5
      },
      monthlyStatistics: [
        { year: 2023, month: 1, monthName: '2023-01', totalLambing: 12, totalLambs: 24, averageLambsPerLambing: 2.0, averageSurvivalRate: 95.8 },
        { year: 2023, month: 2, monthName: '2023-02', totalLambing: 15, totalLambs: 30, averageLambsPerLambing: 2.0, averageSurvivalRate: 96.7 },
        { year: 2023, month: 3, monthName: '2023-03', totalLambing: 18, totalLambs: 36, averageLambsPerLambing: 2.0, averageSurvivalRate: 94.4 },
        { year: 2023, month: 4, monthName: '2023-04', totalLambing: 14, totalLambs: 28, averageLambsPerLambing: 2.0, averageSurvivalRate: 96.4 },
        { year: 2023, month: 5, monthName: '2023-05', totalLambing: 16, totalLambs: 32, averageLambsPerLambing: 2.0, averageSurvivalRate: 95.3 },
        { year: 2023, month: 6, monthName: '2023-06', totalLambing: 13, totalLambs: 26, averageLambsPerLambing: 2.0, averageSurvivalRate: 96.2 },
        { year: 2023, month: 7, monthName: '2023-07', totalLambing: 11, totalLambs: 22, averageLambsPerLambing: 2.0, averageSurvivalRate: 95.5 },
        { year: 2023, month: 8, monthName: '2023-08', totalLambing: 14, totalLambs: 28, averageLambsPerLambing: 2.0, averageSurvivalRate: 94.6 },
        { year: 2023, month: 9, monthName: '2023-09', totalLambing: 17, totalLambs: 34, averageLambsPerLambing: 2.0, averageSurvivalRate: 96.8 },
        { year: 2023, month: 10, monthName: '2023-10', totalLambing: 12, totalLambs: 24, averageLambsPerLambing: 2.0, averageSurvivalRate: 95.8 },
        { year: 2023, month: 11, monthName: '2023-11', totalLambing: 9, totalLambs: 18, averageLambsPerLambing: 2.0, averageSurvivalRate: 96.1 },
        { year: 2023, month: 12, monthName: '2023-12', totalLambing: 5, totalLambs: 10, averageLambsPerLambing: 2.0, averageSurvivalRate: 95.0 }
      ],
      breedStatistics: [
        { breed: '杜泊', totalLambing: 45, totalLambs: 90, averageLambsPerLambing: 2.0, averageSurvivalRate: 96.2 },
        { breed: '澳洲白', totalLambing: 38, totalLambs: 76, averageLambsPerLambing: 2.0, averageSurvivalRate: 95.8 },
        { breed: '萨福克', totalLambing: 28, totalLambs: 56, averageLambsPerLambing: 2.0, averageSurvivalRate: 94.6 },
        { breed: '杜湖', totalLambing: 15, totalLambs: 30, averageLambsPerLambing: 2.0, averageSurvivalRate: 96.7 },
        { breed: '澳湖', totalLambing: 10, totalLambs: 20, averageLambsPerLambing: 2.0, averageSurvivalRate: 95.0 }
      ],
      topEweStatistics: [
        { eweId: 'E001', eweBreed: '杜泊', totalLambing: 8, totalLambs: 16, averageLambsPerLambing: 2.0, lastLambingDate: '2023-12-15' },
        { eweId: 'E002', eweBreed: '澳洲白', totalLambing: 7, totalLambs: 14, averageLambsPerLambing: 2.0, lastLambingDate: '2023-12-10' },
        { eweId: 'E003', eweBreed: '萨福克', totalLambing: 6, totalLambs: 12, averageLambsPerLambing: 2.0, lastLambingDate: '2023-12-08' },
        { eweId: 'E004', eweBreed: '杜泊', totalLambing: 6, totalLambs: 12, averageLambsPerLambing: 2.0, lastLambingDate: '2023-12-05' },
        { eweId: 'E005', eweBreed: '澳洲白', totalLambing: 5, totalLambs: 10, averageLambsPerLambing: 2.0, lastLambingDate: '2023-12-03' }
      ],
      queryParameters: {
        farmId: 'default-farm',
        startDate: undefined,
        endDate: undefined,
        breed: undefined
      }
    }
  },

  // 生成配种统计数据
  breedingStatistics(): BreedingStatistics {
    return {
      summary: {
        totalBreedingRecords: 180,
        successCount: 156,
        successRate: 86.7,
        averagePregnancyDays: 150
      },
      monthlyStatistics: [
        { year: 2023, month: 1, totalBreeding: 15, successCount: 13, successRate: 86.7 },
        { year: 2023, month: 2, totalBreeding: 18, successCount: 16, successRate: 88.9 },
        { year: 2023, month: 3, totalBreeding: 20, successCount: 17, successRate: 85.0 },
        { year: 2023, month: 4, totalBreeding: 16, successCount: 14, successRate: 87.5 },
        { year: 2023, month: 5, totalBreeding: 19, successCount: 16, successRate: 84.2 },
        { year: 2023, month: 6, totalBreeding: 14, successCount: 12, successRate: 85.7 },
        { year: 2023, month: 7, totalBreeding: 12, successCount: 10, successRate: 83.3 },
        { year: 2023, month: 8, totalBreeding: 17, successCount: 15, successRate: 88.2 },
        { year: 2023, month: 9, totalBreeding: 21, successCount: 18, successRate: 85.7 },
        { year: 2023, month: 10, totalBreeding: 15, successCount: 13, successRate: 86.7 },
        { year: 2023, month: 11, totalBreeding: 10, successCount: 9, successRate: 90.0 },
        { year: 2023, month: 12, totalBreeding: 3, successCount: 3, successRate: 100.0 }
      ],
      breedStatistics: [
        { breed: '杜泊', totalBreeding: 45, successCount: 40, successRate: 88.9 },
        { breed: '澳洲白', totalBreeding: 38, successCount: 33, successRate: 86.8 },
        { breed: '萨福克', totalBreeding: 28, successCount: 24, successRate: 85.7 },
        { breed: '杜湖', totalBreeding: 15, successCount: 13, successRate: 86.7 },
        { breed: '澳湖', totalBreeding: 10, successCount: 8, successRate: 80.0 }
      ],
      topRamStatistics: [
        { ramId: 'R001', ramBreed: '杜泊', totalBreeding: 25, successCount: 22, successRate: 88.0, lastBreedingDate: '2023-12-15' },
        { ramId: 'R002', ramBreed: '澳洲白', totalBreeding: 20, successCount: 18, successRate: 90.0, lastBreedingDate: '2023-12-12' },
        { ramId: 'R003', ramBreed: '萨福克', totalBreeding: 15, successCount: 13, successRate: 86.7, lastBreedingDate: '2023-12-10' }
      ],
      topEweStatistics: [
        { eweId: 'E001', eweBreed: '杜泊', totalBreeding: 8, successCount: 7, successRate: 87.5, lastBreedingDate: '2023-12-15' },
        { eweId: 'E002', eweBreed: '澳洲白', totalBreeding: 7, successCount: 6, successRate: 85.7, lastBreedingDate: '2023-12-12' },
        { eweId: 'E003', eweBreed: '萨福克', totalBreeding: 6, successCount: 5, successRate: 83.3, lastBreedingDate: '2023-12-10' }
      ],
      queryParameters: {
        farmId: 'default-farm',
        startDate: undefined,
        endDate: undefined,
        breed: undefined
      }
    }
  },

  // 生成生产统计数据
  productionStatistics(): ProductionStatistics {
    return {
      summary: {
        totalProductionRecords: 245,
        totalBreedingRecords: 180,
        totalAbnormalRecords: 12,
        totalFarrowingRecords: 156,
        totalWeaningRecords: 142,
        totalFemaleSheep: 90,
        totalLambs: 85
      },
      monthlyStatistics: [
        { year: 2023, month: 1, totalProduction: 20, averageWeight: 45.5 },
        { year: 2023, month: 2, totalProduction: 25, averageWeight: 46.2 },
        { year: 2023, month: 3, totalProduction: 30, averageWeight: 47.8 },
        { year: 2023, month: 4, totalProduction: 22, averageWeight: 46.5 },
        { year: 2023, month: 5, totalProduction: 28, averageWeight: 48.1 },
        { year: 2023, month: 6, totalProduction: 24, averageWeight: 47.2 }
      ],
      distributionStatistics: {
        total: 156,
        departure: 12,
        liveLambs: 148,
        deadLambs: 8
      },
      weaningStatistics: {
        total: 142,
        departure: 8,
        deadLambs: 4,
        totalWeight: 6780,
        averageWeight: 47.7
      },
      productionTableData: [
        {
          category: '配种',
          reserveBreeding: 45,
          weaningBreeding: 38,
          returnBreeding: 25,
          unbred: 12,
          emptyBreeding: 8,
          abortionBreeding: 5,
          otherBreeding: 7,
          totalBreeding: 140
        },
        {
          category: '返情',
          reserveBreeding: 0,
          weaningBreeding: 0,
          returnBreeding: 0,
          unbred: 0,
          emptyBreeding: 0,
          abortionBreeding: 0,
          otherBreeding: 0,
          totalBreeding: 0
        },
        {
          category: '异堂',
          reserveBreeding: 0,
          weaningBreeding: 0,
          returnBreeding: 0,
          unbred: 0,
          emptyBreeding: 0,
          abortionBreeding: 0,
          otherBreeding: 0,
          totalBreeding: 0
        },
        {
          category: '分流',
          reserveBreeding: 0,
          weaningBreeding: 0,
          returnBreeding: 0,
          unbred: 0,
          emptyBreeding: 0,
          abortionBreeding: 0,
          otherBreeding: 0,
          totalBreeding: 0
        }
      ]
    }
  },

  // 生成实时存栏数据
  realTimeInventory(): RealTimeInventory[] {
    return [
      { sheepId: 'S001', breed: '杜泊', gender: '公羊', health: 'Healthy', keeperId: 'K001', location: 'A区1号', age: 24 },
      { sheepId: 'S002', breed: '杜泊', gender: '母羊', health: 'Pregnant', keeperId: 'K001', location: 'A区2号', age: 18 },
      { sheepId: 'S003', breed: '澳洲白', gender: '公羊', health: 'Healthy', keeperId: 'K002', location: 'B区1号', age: 30 },
      { sheepId: 'S004', breed: '澳洲白', gender: '母羊', health: 'Healthy', keeperId: 'K002', location: 'B区2号', age: 15 },
      { sheepId: 'S005', breed: '萨福克', gender: '公羊', health: 'Healthy', keeperId: 'K003', location: 'C区1号', age: 36 },
      { sheepId: 'S006', breed: '萨福克', gender: '母羊', health: 'Pregnant', keeperId: 'K003', location: 'C区2号', age: 12 },
      { sheepId: 'S007', breed: '杜湖', gender: '公羊', health: 'Healthy', keeperId: 'K004', location: 'D区1号', age: 42 },
      { sheepId: 'S008', breed: '杜湖', gender: '母羊', health: 'Healthy', keeperId: 'K004', location: 'D区2号', age: 9 },
      { sheepId: 'S009', breed: '澳湖', gender: '公羊', health: 'Healthy', keeperId: 'K005', location: 'E区1号', age: 48 },
      { sheepId: 'S010', breed: '澳湖', gender: '母羊', health: 'Pregnant', keeperId: 'K005', location: 'E区2号', age: 6 }
    ]
  }
} 
﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Summary.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class log1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "report_templates",
                columns: table => new
                {
                    report_id = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    filter_json = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    data_snapshot = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    export_format = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_report_templates", x => x.report_id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sheep",
                columns: table => new
                {
                    sheep_id = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    farm_id = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    breed = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    gender = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    birth_date = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    health = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    keeper_id = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    location = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sheep", x => x.sheep_id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "breeding_records",
                columns: table => new
                {
                    record_id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    ewe_id = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ram_id = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    breeding_date = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    pregnancy_days = table.Column<int>(type: "int", nullable: true),
                    success = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_breeding_records", x => x.record_id);
                    table.ForeignKey(
                        name: "FK_breeding_records_sheep_ewe_id",
                        column: x => x.ewe_id,
                        principalTable: "sheep",
                        principalColumn: "sheep_id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_breeding_records_sheep_ram_id",
                        column: x => x.ram_id,
                        principalTable: "sheep",
                        principalColumn: "sheep_id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "lambing_records",
                columns: table => new
                {
                    record_id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    ewe_id = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    lambing_date = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    lamb_count = table.Column<int>(type: "int", nullable: false),
                    survival_rate = table.Column<float>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_lambing_records", x => x.record_id);
                    table.ForeignKey(
                        name: "FK_lambing_records_sheep_ewe_id",
                        column: x => x.ewe_id,
                        principalTable: "sheep",
                        principalColumn: "sheep_id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "production_records",
                columns: table => new
                {
                    record_id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    ewe_id = table.Column<string>(type: "varchar(95)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    produce_date = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    produce_count = table.Column<int>(type: "int", nullable: false),
                    issues = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_production_records", x => x.record_id);
                    table.ForeignKey(
                        name: "FK_production_records_sheep_ewe_id",
                        column: x => x.ewe_id,
                        principalTable: "sheep",
                        principalColumn: "sheep_id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_breeding_records_breeding_date",
                table: "breeding_records",
                column: "breeding_date");

            migrationBuilder.CreateIndex(
                name: "IX_breeding_records_ewe_id",
                table: "breeding_records",
                column: "ewe_id");

            migrationBuilder.CreateIndex(
                name: "IX_breeding_records_ram_id",
                table: "breeding_records",
                column: "ram_id");

            migrationBuilder.CreateIndex(
                name: "IX_lambing_records_ewe_id",
                table: "lambing_records",
                column: "ewe_id");

            migrationBuilder.CreateIndex(
                name: "IX_lambing_records_lambing_date",
                table: "lambing_records",
                column: "lambing_date");

            migrationBuilder.CreateIndex(
                name: "IX_production_records_ewe_id",
                table: "production_records",
                column: "ewe_id");

            migrationBuilder.CreateIndex(
                name: "IX_production_records_produce_date",
                table: "production_records",
                column: "produce_date");

            migrationBuilder.CreateIndex(
                name: "IX_sheep_breed",
                table: "sheep",
                column: "breed");

            migrationBuilder.CreateIndex(
                name: "IX_sheep_farm_id",
                table: "sheep",
                column: "farm_id");

            migrationBuilder.CreateIndex(
                name: "IX_sheep_gender",
                table: "sheep",
                column: "gender");

            migrationBuilder.CreateIndex(
                name: "IX_sheep_keeper_id",
                table: "sheep",
                column: "keeper_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "breeding_records");

            migrationBuilder.DropTable(
                name: "lambing_records");

            migrationBuilder.DropTable(
                name: "production_records");

            migrationBuilder.DropTable(
                name: "report_templates");

            migrationBuilder.DropTable(
                name: "sheep");
        }
    }
}

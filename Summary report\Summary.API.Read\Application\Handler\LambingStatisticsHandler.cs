using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Read.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.Handler
{
    /// <summary>
    /// 产羔统计处理器
    /// </summary>
    public class LambingStatisticsHandler : 
        IRequestHandler<GetLambingStatisticsCommand, APIResult<object>>,
        IRequestHandler<GenerateLambingReportCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public LambingStatisticsHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取产羔统计
        /// </summary>
        public async Task<APIResult<object>> Handle(GetLambingStatisticsCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var query = _context.LambingRecords
                    .Include(l => l.Ewe)
                    .Where(l => l.Ewe.FarmId == request.FarmId);

                // 按时间段筛选
                if (request.StartDate.HasValue)
                {
                    query = query.Where(l => l.LambingDate >= request.StartDate.Value);
                }
                if (request.EndDate.HasValue)
                {
                    query = query.Where(l => l.LambingDate <= request.EndDate.Value);
                }

                // 按品种筛选
                if (!string.IsNullOrEmpty(request.Breed))
                {
                    query = query.Where(l => l.Ewe.Breed == request.Breed);
                }

                var lambingRecords = await query.ToListAsync(cancellationToken);

                // 按月份统计
                var monthlyStats = lambingRecords
                    .GroupBy(l => new { l.LambingDate.Year, l.LambingDate.Month })
                    .Select(g => new
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        MonthName = $"{g.Key.Year}-{g.Key.Month:D2}",
                        TotalLambing = g.Count(),
                        TotalLambs = g.Sum(l => l.LambCount),
                        AverageLambsPerLambing = g.Average(l => l.LambCount),
                        AverageSurvivalRate = g.Average(l => l.SurvivalRate)
                    })
                    .OrderBy(s => s.Year).ThenBy(s => s.Month)
                    .ToList();

                // 按品种统计
                var breedStats = lambingRecords
                    .GroupBy(l => l.Ewe.Breed)
                    .Select(g => new
                    {
                        Breed = g.Key,
                        TotalLambing = g.Count(),
                        TotalLambs = g.Sum(l => l.LambCount),
                        AverageLambsPerLambing = g.Average(l => l.LambCount),
                        AverageSurvivalRate = g.Average(l => l.SurvivalRate)
                    })
                    .ToList();

                // 按母羊统计
                var eweStats = lambingRecords
                    .GroupBy(l => l.EweId)
                    .Select(g => new
                    {
                        EweId = g.Key,
                        EweBreed = g.First().Ewe.Breed,
                        TotalLambing = g.Count(),
                        TotalLambs = g.Sum(l => l.LambCount),
                        AverageLambsPerLambing = g.Average(l => l.LambCount),
                        LastLambingDate = g.Max(l => l.LambingDate)
                    })
                    .OrderByDescending(s => s.TotalLambing)
                    .Take(10) // 取前10名
                    .ToList();

                var data = new
                {
                    Summary = new
                    {
                        TotalLambingRecords = lambingRecords.Count,
                        TotalLambs = lambingRecords.Sum(l => l.LambCount),
                        AverageLambsPerLambing = lambingRecords.Any() ? lambingRecords.Average(l => l.LambCount) : 0,
                        AverageSurvivalRate = lambingRecords.Any() ? lambingRecords.Average(l => l.SurvivalRate) : 0
                    },
                    MonthlyStatistics = monthlyStats,
                    BreedStatistics = breedStats,
                    TopEweStatistics = eweStats,
                    QueryParameters = new
                    {
                        request.FarmId,
                        request.StartDate,
                        request.EndDate,
                        request.Breed
                    }
                };

                result.Code = ResultCode.Success;
                result.Message = "获取产羔统计成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取产羔统计失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 生成产羔报表
        /// </summary>
        public async Task<APIResult<object>> Handle(GenerateLambingReportCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var statisticsCommand = new GetLambingStatisticsCommand
                {
                    FarmId = request.FarmId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate
                };

                var statisticsResult = await Handle(statisticsCommand, cancellationToken);
                
                var data = new
                {
                    ReportType = "产羔统计报表",
                    Format = request.Format,
                    GeneratedTime = DateTime.Now,
                    FarmId = request.FarmId,
                    Statistics = statisticsResult.Data,
                    ExportUrl = $"/api/reports/lambing/{request.FarmId}?format={request.Format.ToLower()}"
                };

                result.Code = ResultCode.Success;
                result.Message = "生成产羔报表成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"生成产羔报表失败: {ex.Message}";
            }

            return result;
        }
    }
} 
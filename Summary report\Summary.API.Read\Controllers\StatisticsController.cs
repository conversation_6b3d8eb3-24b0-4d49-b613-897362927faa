using MediatR;
using Microsoft.AspNetCore.Mvc;
using Summary.API.Read.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Read.Controllers
{
    /// <summary>
    /// 统计控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class StatisticsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public StatisticsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        #region 羊场存栏统计

        /// <summary>
        /// 获取羊场存栏统计
        /// </summary>
        [HttpGet("inventory/{farmId}")]
        public async Task<APIResult<object>> GetInventoryStatistics(
            string farmId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var request = new GetInventoryStatisticsCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate
            };

            return await _mediator.Send(request);
        }

        /// <summary>
        /// 获取羊场存栏历史趋势
        /// </summary>
        [HttpGet("inventory/{farmId}/trend")]
        public async Task<APIResult<object>> GetInventoryHistoryTrend(
            string farmId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            var request = new GetInventoryHistoryTrendCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate
            };

            return await _mediator.Send(request);
        }

        /// <summary>
        /// 生成羊场存栏报表
        /// </summary>
        [HttpGet("inventory/{farmId}/report")]
        public async Task<APIResult<object>> GenerateInventoryReport(
            string farmId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var request = new GenerateInventoryReportCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate
            };

            return await _mediator.Send(request);
        }

        #endregion

        #region 羊场实时存栏

        /// <summary>
        /// 获取羊场实时存栏
        /// </summary>
        [HttpGet("realtime/{farmId}")]
        public async Task<APIResult<object>> GetRealTimeInventory(
            string farmId,
            [FromQuery] string? displayMode = "list")
        {
            var request = new GetRealTimeInventoryCommand
            {
                FarmId = farmId,
                DisplayMode = displayMode
            };

            return await _mediator.Send(request);
        }

        /// <summary>
        /// 获取羊只位置地图
        /// </summary>
        [HttpGet("realtime/{farmId}/map")]
        public async Task<APIResult<object>> GetSheepLocationMap(string farmId)
        {
            var request = new GetSheepLocationMapCommand
            {
                FarmId = farmId
            };

            return await _mediator.Send(request);
        }

        /// <summary>
        /// 获取羊只详情
        /// </summary>
        [HttpGet("sheep/{sheepId}/details")]
        public async Task<APIResult<object>> GetSheepDetails(string sheepId)
        {
            var request = new GetSheepDetailsCommand
            {
                SheepId = sheepId
            };

            return await _mediator.Send(request);
        }

        #endregion

        #region 产羔统计

        /// <summary>
        /// 获取产羔统计
        /// </summary>
        [HttpGet("lambing/{farmId}")]
        public async Task<APIResult<object>> GetLambingStatistics(
            string farmId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? breed = null)
        {
            var request = new GetLambingStatisticsCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate,
                Breed = breed
            };

            return await _mediator.Send(request);
        }

        /// <summary>
        /// 生成产羔报表
        /// </summary>
        [HttpGet("lambing/{farmId}/report")]
        public async Task<APIResult<object>> GenerateLambingReport(
            string farmId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string format = "Excel")
        {
            var request = new GenerateLambingReportCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate,
                Format = format
            };

            return await _mediator.Send(request);
        }

        #endregion

        #region 配种统计

        /// <summary>
        /// 获取配种统计
        /// </summary>
        [HttpGet("breeding/{farmId}")]
        public async Task<APIResult<object>> GetBreedingStatistics(
            string farmId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? breed = null)
        {
            var request = new GetBreedingStatisticsCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate,
                Breed = breed
            };

            return await _mediator.Send(request);
        }

        /// <summary>
        /// 生成配种报表
        /// </summary>
        [HttpGet("breeding/{farmId}/report")]
        public async Task<APIResult<object>> GenerateBreedingReport(
            string farmId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string format = "Excel")
        {
            var request = new GenerateBreedingReportCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate,
                Format = format
            };

            return await _mediator.Send(request);
        }

        #endregion

        #region 生产统计

        /// <summary>
        /// 获取生产统计
        /// </summary>
        [HttpGet("production/{farmId}")]
        public async Task<APIResult<object>> GetProductionStatistics(
            string farmId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? breed = null)
        {
            var request = new GetProductionStatisticsCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate,
                Breed = breed
            };

            return await _mediator.Send(request);
        }

        /// <summary>
        /// 生成生产报表
        /// </summary>
        [HttpGet("production/{farmId}/report")]
        public async Task<APIResult<object>> GenerateProductionReport(
            string farmId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string format = "Excel")
        {
            var request = new GenerateProductionReportCommand
            {
                FarmId = farmId,
                StartDate = startDate,
                EndDate = endDate,
                Format = format
            };

            return await _mediator.Send(request);
        }

        #endregion
    }
} 
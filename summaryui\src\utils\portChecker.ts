// 端口检查工具
export const checkBackendPorts = async () => {
  const ports = [5251, 5110] // Read API 和 Write API 端口
  const results: Record<number, boolean> = {}
  
  for (const port of ports) {
    try {
      const response = await fetch(`http://localhost:${port}/api/statistics/inventory/default-farm`, {
        method: 'GET',
        mode: 'no-cors', // 避免CORS错误
        cache: 'no-cache'
      })
      results[port] = true
    } catch (error) {
      results[port] = false
    }
  }
  
  return results
}

// 获取可用的后端服务信息
export const getAvailableBackendInfo = () => {
  const portInfo = {
    5251: 'Summary.API.Read (统计查询服务)',
    5110: 'Summary.API.Write (记录管理服务)'
  }
  
  return portInfo
}

// 检查并返回推荐的后端端口
export const getRecommendedBackendPort = async () => {
  const portStatus = await checkBackendPorts()
  
  // 优先使用Read API端口
  if (portStatus[5251]) {
    return 5251
  }
  
  // 如果Read API不可用，尝试Write API
  if (portStatus[5110]) {
    return 5110
  }
  
  // 如果都不可用，返回默认端口
  return 5251
} 
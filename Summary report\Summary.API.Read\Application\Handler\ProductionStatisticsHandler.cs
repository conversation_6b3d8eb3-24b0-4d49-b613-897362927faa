using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Read.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.Handler
{
    /// <summary>
    /// 生产统计处理器
    /// </summary>
    public class ProductionStatisticsHandler : 
        IRequestHandler<GetProductionStatisticsCommand, APIResult<object>>,
        IRequestHandler<GenerateProductionReportCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public ProductionStatisticsHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取生产统计
        /// </summary>
        public async Task<APIResult<object>> Handle(GetProductionStatisticsCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var query = _context.ProductionRecords
                    .Include(p => p.Ewe)
                    .Where(p => p.Ewe.FarmId == request.FarmId);

                // 按时间段筛选
                if (request.StartDate.HasValue)
                {
                    query = query.Where(p => p.ProduceDate >= request.StartDate.Value);
                }
                if (request.EndDate.HasValue)
                {
                    query = query.Where(p => p.ProduceDate <= request.EndDate.Value);
                }

                // 按品种筛选
                if (!string.IsNullOrEmpty(request.Breed))
                {
                    query = query.Where(p => p.Ewe.Breed == request.Breed);
                }

                var productionRecords = await query.ToListAsync(cancellationToken);

                // 按月份统计
                var monthlyStats = productionRecords
                    .GroupBy(p => new { p.ProduceDate.Year, p.ProduceDate.Month })
                    .Select(g => new
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        MonthName = $"{g.Key.Year}-{g.Key.Month:D2}",
                        TotalProduction = g.Count(),
                        TotalProduceCount = g.Sum(p => p.ProduceCount),
                        AverageProduceCount = g.Average(p => p.ProduceCount),
                        IssuesCount = g.Count(p => !string.IsNullOrEmpty(p.Issues))
                    })
                    .OrderBy(s => s.Year).ThenBy(s => s.Month)
                    .ToList();

                // 按品种统计
                var breedStats = productionRecords
                    .GroupBy(p => p.Ewe.Breed)
                    .Select(g => new
                    {
                        Breed = g.Key,
                        TotalProduction = g.Count(),
                        TotalProduceCount = g.Sum(p => p.ProduceCount),
                        AverageProduceCount = g.Average(p => p.ProduceCount),
                        IssuesCount = g.Count(p => !string.IsNullOrEmpty(p.Issues))
                    })
                    .ToList();

                // 按母羊统计生产效率
                var eweEfficiencyStats = productionRecords
                    .GroupBy(p => p.EweId)
                    .Select(g => new
                    {
                        EweId = g.Key,
                        EweBreed = g.First().Ewe.Breed,
                        TotalProduction = g.Count(),
                        TotalProduceCount = g.Sum(p => p.ProduceCount),
                        AverageProduceCount = g.Average(p => p.ProduceCount),
                        LastProductionDate = g.Max(p => p.ProduceDate),
                        IssuesCount = g.Count(p => !string.IsNullOrEmpty(p.Issues))
                    })
                    .OrderByDescending(s => s.AverageProduceCount)
                    .Take(10) // 取前10名
                    .ToList();

                // 问题分析
                var issuesAnalysis = productionRecords
                    .Where(p => !string.IsNullOrEmpty(p.Issues))
                    .GroupBy(p => p.Issues)
                    .Select(g => new
                    {
                        Issue = g.Key,
                        Count = g.Count(),
                        Percentage = (double)g.Count() / productionRecords.Count * 100
                    })
                    .OrderByDescending(x => x.Count)
                    .ToList();

                var data = new
                {
                    Summary = new
                    {
                        TotalProductionRecords = productionRecords.Count,
                        TotalProduceCount = productionRecords.Sum(p => p.ProduceCount),
                        AverageProduceCount = productionRecords.Any() ? productionRecords.Average(p => p.ProduceCount) : 0,
                        IssuesCount = productionRecords.Count(p => !string.IsNullOrEmpty(p.Issues)),
                        IssuesRate = productionRecords.Count > 0 ? (double)productionRecords.Count(p => !string.IsNullOrEmpty(p.Issues)) / productionRecords.Count * 100 : 0
                    },
                    MonthlyStatistics = monthlyStats,
                    BreedStatistics = breedStats,
                    TopEweEfficiencyStatistics = eweEfficiencyStats,
                    IssuesAnalysis = issuesAnalysis,
                    QueryParameters = new
                    {
                        request.FarmId,
                        request.StartDate,
                        request.EndDate,
                        request.Breed
                    }
                };

                result.Code = ResultCode.Success;
                result.Message = "获取生产统计成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取生产统计失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 生成生产报表
        /// </summary>
        public async Task<APIResult<object>> Handle(GenerateProductionReportCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var statisticsCommand = new GetProductionStatisticsCommand
                {
                    FarmId = request.FarmId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate
                };

                var statisticsResult = await Handle(statisticsCommand, cancellationToken);
                
                var data = new
                {
                    ReportType = "生产统计报表",
                    Format = request.Format,
                    GeneratedTime = DateTime.Now,
                    FarmId = request.FarmId,
                    Statistics = statisticsResult.Data,
                    ExportUrl = $"/api/reports/production/{request.FarmId}?format={request.Format.ToLower()}"
                };

                result.Code = ResultCode.Success;
                result.Message = "生成生产报表成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"生成生产报表失败: {ex.Message}";
            }

            return result;
        }
    }
} 
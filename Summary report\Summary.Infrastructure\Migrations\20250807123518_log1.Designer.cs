﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Summary.Infrastructure;

#nullable disable

namespace Summary.Infrastructure.Migrations
{
    [DbContext(typeof(MyDbContext))]
    [Migration("20250807123518_log1")]
    partial class log1
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Summary.Domain.Entities.BreedingRecord", b =>
                {
                    b.Property<long>("RecordId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("record_id");

                    b.Property<DateTime>("BreedingDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("breeding_date");

                    b.Property<string>("EweId")
                        .IsRequired()
                        .HasColumnType("varchar(95)")
                        .HasColumnName("ewe_id");

                    b.Property<int?>("PregnancyDays")
                        .HasColumnType("int")
                        .HasColumnName("pregnancy_days");

                    b.Property<string>("RamId")
                        .IsRequired()
                        .HasColumnType("varchar(95)")
                        .HasColumnName("ram_id");

                    b.Property<bool>("Success")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("success");

                    b.HasKey("RecordId");

                    b.HasIndex("BreedingDate");

                    b.HasIndex("EweId");

                    b.HasIndex("RamId");

                    b.ToTable("breeding_records");
                });

            modelBuilder.Entity("Summary.Domain.Entities.LambingRecord", b =>
                {
                    b.Property<long>("RecordId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("record_id");

                    b.Property<string>("EweId")
                        .IsRequired()
                        .HasColumnType("varchar(95)")
                        .HasColumnName("ewe_id");

                    b.Property<int>("LambCount")
                        .HasColumnType("int")
                        .HasColumnName("lamb_count");

                    b.Property<DateTime>("LambingDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("lambing_date");

                    b.Property<float>("SurvivalRate")
                        .HasColumnType("float")
                        .HasColumnName("survival_rate");

                    b.HasKey("RecordId");

                    b.HasIndex("EweId");

                    b.HasIndex("LambingDate");

                    b.ToTable("lambing_records");
                });

            modelBuilder.Entity("Summary.Domain.Entities.ProductionRecord", b =>
                {
                    b.Property<long>("RecordId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("record_id");

                    b.Property<string>("EweId")
                        .IsRequired()
                        .HasColumnType("varchar(95)")
                        .HasColumnName("ewe_id");

                    b.Property<string>("Issues")
                        .HasColumnType("longtext")
                        .HasColumnName("issues");

                    b.Property<int>("ProduceCount")
                        .HasColumnType("int")
                        .HasColumnName("produce_count");

                    b.Property<DateTime>("ProduceDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("produce_date");

                    b.HasKey("RecordId");

                    b.HasIndex("EweId");

                    b.HasIndex("ProduceDate");

                    b.ToTable("production_records");
                });

            modelBuilder.Entity("Summary.Domain.Entities.ReportTemplate", b =>
                {
                    b.Property<string>("ReportId")
                        .HasColumnType("varchar(95)")
                        .HasColumnName("report_id");

                    b.Property<string>("DataSnapshot")
                        .HasColumnType("longtext")
                        .HasColumnName("data_snapshot");

                    b.Property<string>("ExportFormat")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("export_format");

                    b.Property<string>("FilterJson")
                        .HasColumnType("longtext")
                        .HasColumnName("filter_json");

                    b.HasKey("ReportId");

                    b.ToTable("report_templates");
                });

            modelBuilder.Entity("Summary.Domain.Entities.Sheep", b =>
                {
                    b.Property<string>("SheepId")
                        .HasColumnType("varchar(95)")
                        .HasColumnName("sheep_id");

                    b.Property<DateTime?>("BirthDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("birth_date");

                    b.Property<string>("Breed")
                        .IsRequired()
                        .HasColumnType("varchar(95)")
                        .HasColumnName("breed");

                    b.Property<string>("FarmId")
                        .IsRequired()
                        .HasColumnType("varchar(95)")
                        .HasColumnName("farm_id");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasColumnType("varchar(95)")
                        .HasColumnName("gender");

                    b.Property<string>("Health")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("health");

                    b.Property<string>("KeeperId")
                        .IsRequired()
                        .HasColumnType("varchar(95)")
                        .HasColumnName("keeper_id");

                    b.Property<string>("Location")
                        .HasColumnType("longtext")
                        .HasColumnName("location");

                    b.HasKey("SheepId");

                    b.HasIndex("Breed");

                    b.HasIndex("FarmId");

                    b.HasIndex("Gender");

                    b.HasIndex("KeeperId");

                    b.ToTable("sheep");
                });

            modelBuilder.Entity("Summary.Domain.Entities.BreedingRecord", b =>
                {
                    b.HasOne("Summary.Domain.Entities.Sheep", "Ewe")
                        .WithMany("BreedingRecordsAsEwe")
                        .HasForeignKey("EweId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Summary.Domain.Entities.Sheep", "Ram")
                        .WithMany("BreedingRecordsAsRam")
                        .HasForeignKey("RamId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Ewe");

                    b.Navigation("Ram");
                });

            modelBuilder.Entity("Summary.Domain.Entities.LambingRecord", b =>
                {
                    b.HasOne("Summary.Domain.Entities.Sheep", "Ewe")
                        .WithMany("LambingRecords")
                        .HasForeignKey("EweId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Ewe");
                });

            modelBuilder.Entity("Summary.Domain.Entities.ProductionRecord", b =>
                {
                    b.HasOne("Summary.Domain.Entities.Sheep", "Ewe")
                        .WithMany("ProductionRecords")
                        .HasForeignKey("EweId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Ewe");
                });

            modelBuilder.Entity("Summary.Domain.Entities.Sheep", b =>
                {
                    b.Navigation("BreedingRecordsAsEwe");

                    b.Navigation("BreedingRecordsAsRam");

                    b.Navigation("LambingRecords");

                    b.Navigation("ProductionRecords");
                });
#pragma warning restore 612, 618
        }
    }
}

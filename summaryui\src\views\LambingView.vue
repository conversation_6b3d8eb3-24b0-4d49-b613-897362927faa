<template>
  <div class="lambing-view">
    <!-- 返回首页按钮 -->
    <BackToHome />
    
    <!-- 导航标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="月产羔结构" name="monthly">
        <div class="tab-content">
          <!-- 查询条件 -->
          <div class="dashboard-card query-section">
            <el-form :model="queryForm" inline>
              <el-form-item label="年份">
                <el-input v-model="queryForm.year" placeholder="请输入年份" style="width: 120px" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleQuery" :loading="apiStore.loading">
                  <el-icon><Search /></el-icon>
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 产羔统计图表 -->
          <div class="dashboard-card chart-section">
            <div class="chart-container" ref="lambingChartRef"></div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="窝均产活羔" name="average">
        <div class="tab-content">
          <!-- 查询条件 -->
          <div class="dashboard-card query-section">
            <el-form :model="queryForm" inline>
              <el-form-item label="日期">
                <el-date-picker
                  v-model="queryForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleQuery" :loading="apiStore.loading">
                  <el-icon><Search /></el-icon>
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 窝均产活羔统计 -->
          <div class="dashboard-card stats-section">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-number">{{ averageLiveLambs }}</div>
                  <div class="stat-label">窝均产活羔数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-number">{{ totalLitters }}</div>
                  <div class="stat-label">总窝数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-number">{{ totalLiveLambs }}</div>
                  <div class="stat-label">总活羔数</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="胎次产羔结构" name="parity">
        <div class="tab-content">
          <div class="dashboard-card">
            <div class="chart-container" ref="parityChartRef"></div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="产羔哺乳情况" name="lactation">
        <div class="tab-content">
          <div class="dashboard-card">
            <div class="chart-container" ref="lactationChartRef"></div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="产羔哺乳详细情况" name="detailed">
        <div class="tab-content">
          <div class="dashboard-card">
            <el-table :data="detailedData" border stripe>
              <el-table-column prop="date" label="日期" width="120" />
              <el-table-column prop="sheepId" label="羊只编号" width="120" />
              <el-table-column prop="breed" label="品种" width="100" />
              <el-table-column prop="liveLambs" label="活羔数" width="80" />
              <el-table-column prop="weakLambs" label="弱羔数" width="80" />
              <el-table-column prop="deformed" label="畸形数" width="80" />
              <el-table-column prop="stillbirth" label="死胎数" width="80" />
              <el-table-column prop="total" label="总数" width="80" />
              <el-table-column prop="lactationStatus" label="哺乳状态" width="100" />
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useApiStore } from '@/stores/api'
import { Search } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import BackToHome from '@/components/BackToHome.vue'

const apiStore = useApiStore()

// 响应式数据
const activeTab = ref('monthly')
const queryForm = reactive({
  year: '2023',
  dateRange: ['2022-03-01', '2023-04-01']
})

// 统计数据
const averageLiveLambs = ref(0)
const totalLitters = ref(0)
const totalLiveLambs = ref(0)

// 详细数据
const detailedData = ref([
  {
    date: '2023-04-01',
    sheepId: 'S001',
    breed: '杜泊',
    liveLambs: 2,
    weakLambs: 0,
    deformed: 0,
    stillbirth: 0,
    total: 2,
    lactationStatus: '正常'
  }
])

// 图表引用
const lambingChartRef = ref<HTMLElement>()
const parityChartRef = ref<HTMLElement>()
const lactationChartRef = ref<HTMLElement>()

// 处理标签页点击
const handleTabClick = (tab: any) => {
  nextTick(() => {
    switch (tab.props.name) {
      case 'monthly':
        initLambingChart({})
        break
      case 'parity':
        initParityChart({})
        break
      case 'lactation':
        initLactationChart({})
        break
    }
  })
}

// 查询处理
const handleQuery = async () => {
  try {
    const params = {
      startDate: queryForm.dateRange[0],
      endDate: queryForm.dateRange[1],
      year: queryForm.year
    }
    
    const response = await apiStore.getLambingStatistics(params)
    if (response && response.data) {
      updateChartData(response.data)
    }
  } catch (error) {
    console.error('查询失败:', error)
  }
}

// 更新图表数据
const updateChartData = (data: any) => {
  // 根据后端返回的数据更新图表
  if (data) {
    // 更新统计数据
    if (data.Summary) {
      averageLiveLambs.value = Math.round(data.Summary.AverageLambsPerLambing * 100) / 100
      totalLitters.value = data.Summary.TotalLambingRecords
      totalLiveLambs.value = data.Summary.TotalLambs
    }
    
    // 更新详细数据
    if (data.TopEweStatistics) {
      detailedData.value = data.TopEweStatistics.map((item: any) => ({
        date: item.LastLambingDate ? new Date(item.LastLambingDate).toISOString().split('T')[0] : '',
        sheepId: item.EweId,
        breed: item.EweBreed,
        liveLambs: item.TotalLambs,
        weakLambs: 0,
        deformed: 0,
        stillbirth: 0,
        total: item.TotalLambs,
        lactationStatus: '正常'
      }))
    }
    
    // 重新初始化图表
    nextTick(() => {
      initLambingChart(data)
      initParityChart(data)
      initLactationChart(data)
    })
  }
}

// 初始化产羔统计图表
const initLambingChart = (data: any) => {
  if (!lambingChartRef.value) return
  
  const chart = echarts.init(lambingChartRef.value)
  
  // 根据真实数据构建图表数据
  const monthlyStats = data?.MonthlyStatistics || []
  const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
  
  const chartData = months.map((month, index) => {
    const monthData = monthlyStats.find((item: any) => item.Month === `${queryForm.year}-${String(index + 1).padStart(2, '0')}`)
    return monthData ? monthData.AverageLambsPerLambing : 0
  })
  
  const option = {
    title: {
      text: '平均产只数',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((param: any) => {
          result += param.marker + param.seriesName + ': ' + param.value + '<br/>'
        })
        return result
      }
    },
    legend: {
      data: ['平均产羔数'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: Math.max(...chartData, 1)
    },
    series: [
      {
        name: '平均产羔数',
        type: 'line',
        data: chartData,
        itemStyle: { color: '#5470c6' },
        symbol: 'circle'
      }
    ]
  }
  chart.setOption(option)
}

// 初始化胎次产羔结构图表
const initParityChart = (data: any) => {
  if (!parityChartRef.value) return
  
  const chart = echarts.init(parityChartRef.value)
  
  // 根据真实数据构建图表数据
  const breedStats = data?.BreedStatistics || []
  const chartData = breedStats.map((item: any) => ({
    value: item.TotalLambing,
    name: item.Breed,
    itemStyle: { color: getBreedColor(item.Breed) }
  })).filter((item: any) => item.value > 0)
  
  const option = {
    title: {
      text: '品种产羔结构',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}次 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: chartData.map((item: any) => item.name)
    },
    series: [
      {
        name: '产羔统计',
        type: 'pie',
        radius: '50%',
        center: ['60%', '50%'],
        data: chartData
      }
    ]
  }
  chart.setOption(option)
}

// 初始化产羔哺乳情况图表
const initLactationChart = (data: any) => {
  if (!lactationChartRef.value) return
  
  const chart = echarts.init(lactationChartRef.value)
  
  // 根据真实数据构建图表数据
  const monthlyStats = data?.MonthlyStatistics || []
  const months = ['一月', '二月', '三月', '四月', '五月', '六月']
  
  const normalData = months.map((month, index) => {
    const monthData = monthlyStats.find((item: any) => item.Month === `${queryForm.year}-${String(index + 1).padStart(2, '0')}`)
    return monthData ? Math.round(monthData.TotalLambing * 0.8) : 0 // 假设80%正常哺乳
  })
  
  const abnormalData = months.map((month, index) => {
    const monthData = monthlyStats.find((item: any) => item.Month === `${queryForm.year}-${String(index + 1).padStart(2, '0')}`)
    return monthData ? Math.round(monthData.TotalLambing * 0.15) : 0 // 假设15%异常哺乳
  })
  
  const noData = months.map((month, index) => {
    const monthData = monthlyStats.find((item: any) => item.Month === `${queryForm.year}-${String(index + 1).padStart(2, '0')}`)
    return monthData ? Math.round(monthData.TotalLambing * 0.05) : 0 // 假设5%无哺乳
  })
  
  const option = {
    title: {
      text: '产羔哺乳情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['正常哺乳', '异常哺乳', '无哺乳']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '正常哺乳',
        type: 'bar',
        data: normalData,
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '异常哺乳',
        type: 'bar',
        data: abnormalData,
        itemStyle: { color: '#fac858' }
      },
      {
        name: '无哺乳',
        type: 'bar',
        data: noData,
        itemStyle: { color: '#ee6666' }
      }
    ]
  }
  chart.setOption(option)
}

// 获取品种颜色
const getBreedColor = (breed: string) => {
  const colorMap: Record<string, string> = {
    '杜泊': '#3498db',
    '澳洲白': '#2ecc71',
    '萨福克': '#f1c40f',
    '杜湖': '#e74c3c',
    '澳湖': '#9b59b6'
  }
  return colorMap[breed] || '#95a5a6'
}

onMounted(async () => {
  await handleQuery()
  
  await nextTick()
  initLambingChart({})
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    const lambingChart = echarts.getInstanceByDom(lambingChartRef.value!)
    const parityChart = echarts.getInstanceByDom(parityChartRef.value!)
    const lactationChart = echarts.getInstanceByDom(lactationChartRef.value!)
    lambingChart?.resize()
    parityChart?.resize()
    lactationChart?.resize()
  })
})
</script>

<style scoped>
.lambing-view {
  padding: 20px 0;
}

.tab-content {
  padding: 20px 0;
}

.query-section {
  margin-bottom: 20px;
  padding: 20px;
}

.chart-section {
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 20px;
  padding: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 10px;
}

.stat-label {
  color: #606266;
  font-size: 0.9rem;
}

.chart-container {
  height: 400px;
  padding: 20px;
}

@media (max-width: 768px) {
  .stat-number {
    font-size: 2rem;
  }
  
  .chart-container {
    height: 300px;
  }
}
</style> 
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Summary.ErrorCode
{
    /// <summary>
    /// API分页结果封装类 - 用于处理分页查询的返回结果
    /// </summary>
    /// <typeparam name="T">分页数据的类型</typeparam>
    public class APIPageing<T> where T : class
    {
        /// <summary>
        /// 总记录数 - 数据库中符合条件的总记录数量
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 总页数 - 根据每页大小计算出的总页数
        /// </summary>
        public int PageCount { get; set; }
        
        /// <summary>
        /// 分页数据集合 - 当前页的数据列表
        /// </summary>
        public List<T> PageData { get; set; } = new List<T>();
    }
}

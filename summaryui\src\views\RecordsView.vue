<template>
  <div class="records-view">
    <!-- 返回首页按钮 -->
    <BackToHome />
    
    <!-- 导航标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="产羔记录" name="lambing">
        <div class="tab-content">
          <!-- 添加产羔记录 -->
          <div class="dashboard-card form-section">
            <div class="section-header">
              <h3>添加产羔记录</h3>
            </div>
            <el-form :model="lambingForm" :rules="lambingRules" ref="lambingFormRef" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="羊只编号" prop="sheepId">
                    <el-input v-model="lambingForm.sheepId" placeholder="请输入羊只编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="品种" prop="breed">
                    <el-select v-model="lambingForm.breed" placeholder="请选择品种" style="width: 100%">
                      <el-option label="杜泊" value="杜泊" />
                      <el-option label="澳洲白" value="澳洲白" />
                      <el-option label="萨福克" value="萨福克" />
                      <el-option label="杜湖" value="杜湖" />
                      <el-option label="澳湖" value="澳湖" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="产羔日期" prop="lambingDate">
                    <el-date-picker
                      v-model="lambingForm.lambingDate"
                      type="date"
                      placeholder="选择产羔日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="活羔数" prop="liveLambs">
                    <el-input-number v-model="lambingForm.liveLambs" :min="0" :max="10" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="弱羔数" prop="weakLambs">
                    <el-input-number v-model="lambingForm.weakLambs" :min="0" :max="10" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="死胎数" prop="stillbirth">
                    <el-input-number v-model="lambingForm.stillbirth" :min="0" :max="10" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="畸形数" prop="deformed">
                    <el-input-number v-model="lambingForm.deformed" :min="0" :max="10" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="哺乳状态" prop="lactationStatus">
                    <el-select v-model="lambingForm.lactationStatus" placeholder="请选择哺乳状态" style="width: 100%">
                      <el-option label="正常" value="正常" />
                      <el-option label="异常" value="异常" />
                      <el-option label="无哺乳" value="无哺乳" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="备注" prop="notes">
                <el-input v-model="lambingForm.notes" type="textarea" :rows="3" placeholder="请输入备注信息" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitLambingRecord" :loading="apiStore.loading">
                  提交产羔记录
                </el-button>
                <el-button @click="resetLambingForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 产羔记录列表 -->
          <div class="dashboard-card table-section">
            <div class="table-header">
              <h3>产羔记录列表</h3>
              <div class="table-actions">
                <el-button size="small" @click="refreshLambingRecords">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
            <el-table :data="lambingRecords" border stripe>
              <el-table-column prop="sheepId" label="羊只编号" width="120" />
              <el-table-column prop="breed" label="品种" width="100" />
              <el-table-column prop="lambingDate" label="产羔日期" width="120" />
              <el-table-column prop="liveLambs" label="活羔数" width="80" />
              <el-table-column prop="weakLambs" label="弱羔数" width="80" />
              <el-table-column prop="stillbirth" label="死胎数" width="80" />
              <el-table-column prop="deformed" label="畸形数" width="80" />
              <el-table-column prop="lactationStatus" label="哺乳状态" width="100" />
              <el-table-column prop="notes" label="备注" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button size="small" @click="editLambingRecord(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteLambingRecord(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="配种记录" name="breeding">
        <div class="tab-content">
          <!-- 添加配种记录 -->
          <div class="dashboard-card form-section">
            <div class="section-header">
              <h3>添加配种记录</h3>
            </div>
            <el-form :model="breedingForm" :rules="breedingRules" ref="breedingFormRef" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="母羊编号" prop="femaleSheepId">
                    <el-input v-model="breedingForm.femaleSheepId" placeholder="请输入母羊编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="公羊编号" prop="maleSheepId">
                    <el-input v-model="breedingForm.maleSheepId" placeholder="请输入公羊编号" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="配种日期" prop="breedingDate">
                    <el-date-picker
                      v-model="breedingForm.breedingDate"
                      type="date"
                      placeholder="选择配种日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="配种方式" prop="breedingMethod">
                    <el-select v-model="breedingForm.breedingMethod" placeholder="请选择配种方式" style="width: 100%">
                      <el-option label="自然配种" value="自然配种" />
                      <el-option label="人工授精" value="人工授精" />
                      <el-option label="胚胎移植" value="胚胎移植" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="配种状态" prop="breedingStatus">
                    <el-select v-model="breedingForm.breedingStatus" placeholder="请选择配种状态" style="width: 100%">
                      <el-option label="成功" value="成功" />
                      <el-option label="失败" value="失败" />
                      <el-option label="待确认" value="待确认" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="预计分娩日期" prop="expectedFarrowingDate">
                    <el-date-picker
                      v-model="breedingForm.expectedFarrowingDate"
                      type="date"
                      placeholder="选择预计分娩日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="备注" prop="notes">
                <el-input v-model="breedingForm.notes" type="textarea" :rows="3" placeholder="请输入备注信息" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitBreedingRecord" :loading="apiStore.loading">
                  提交配种记录
                </el-button>
                <el-button @click="resetBreedingForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 配种记录列表 -->
          <div class="dashboard-card table-section">
            <div class="table-header">
              <h3>配种记录列表</h3>
              <div class="table-actions">
                <el-button size="small" @click="refreshBreedingRecords">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
            <el-table :data="breedingRecords" border stripe>
              <el-table-column prop="femaleSheepId" label="母羊编号" width="120" />
              <el-table-column prop="maleSheepId" label="公羊编号" width="120" />
              <el-table-column prop="breedingDate" label="配种日期" width="120" />
              <el-table-column prop="breedingMethod" label="配种方式" width="100" />
              <el-table-column prop="breedingStatus" label="配种状态" width="100" />
              <el-table-column prop="expectedFarrowingDate" label="预计分娩日期" width="120" />
              <el-table-column prop="notes" label="备注" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button size="small" @click="editBreedingRecord(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteBreedingRecord(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="生产记录" name="production">
        <div class="tab-content">
          <!-- 添加生产记录 -->
          <div class="dashboard-card form-section">
            <div class="section-header">
              <h3>添加生产记录</h3>
            </div>
            <el-form :model="productionForm" :rules="productionRules" ref="productionFormRef" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="羊只编号" prop="sheepId">
                    <el-input v-model="productionForm.sheepId" placeholder="请输入羊只编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="记录类型" prop="recordType">
                    <el-select v-model="productionForm.recordType" placeholder="请选择记录类型" style="width: 100%">
                      <el-option label="体重记录" value="体重记录" />
                      <el-option label="采食记录" value="采食记录" />
                      <el-option label="防疫记录" value="防疫记录" />
                      <el-option label="疾病记录" value="疾病记录" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="记录日期" prop="recordDate">
                    <el-date-picker
                      v-model="productionForm.recordDate"
                      type="date"
                      placeholder="选择记录日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="数值" prop="value">
                    <el-input v-model="productionForm.value" placeholder="请输入数值" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="备注" prop="notes">
                <el-input v-model="productionForm.notes" type="textarea" :rows="3" placeholder="请输入备注信息" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitProductionRecord" :loading="apiStore.loading">
                  提交生产记录
                </el-button>
                <el-button @click="resetProductionForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 生产记录列表 -->
          <div class="dashboard-card table-section">
            <div class="table-header">
              <h3>生产记录列表</h3>
              <div class="table-actions">
                <el-button size="small" @click="refreshProductionRecords">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
            <el-table :data="productionRecords" border stripe>
              <el-table-column prop="sheepId" label="羊只编号" width="120" />
              <el-table-column prop="recordType" label="记录类型" width="100" />
              <el-table-column prop="recordDate" label="记录日期" width="120" />
              <el-table-column prop="value" label="数值" width="100" />
              <el-table-column prop="notes" label="备注" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button size="small" @click="editProductionRecord(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteProductionRecord(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useApiStore } from '@/stores/api'
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import BackToHome from '@/components/BackToHome.vue'

const apiStore = useApiStore()

// 响应式数据
const activeTab = ref('lambing')

// 表单引用
const lambingFormRef = ref<FormInstance>()
const breedingFormRef = ref<FormInstance>()
const productionFormRef = ref<FormInstance>()

// 产羔记录表单
const lambingForm = reactive({
  sheepId: '',
  breed: '',
  lambingDate: '',
  liveLambs: 0,
  weakLambs: 0,
  stillbirth: 0,
  deformed: 0,
  lactationStatus: '',
  notes: ''
})

// 配种记录表单
const breedingForm = reactive({
  femaleSheepId: '',
  maleSheepId: '',
  breedingDate: '',
  breedingMethod: '',
  breedingStatus: '',
  expectedFarrowingDate: '',
  notes: ''
})

// 生产记录表单
const productionForm = reactive({
  sheepId: '',
  recordType: '',
  recordDate: '',
  value: '',
  notes: ''
})

// 表单验证规则
const lambingRules: FormRules = {
  sheepId: [{ required: true, message: '请输入羊只编号', trigger: 'blur' }],
  breed: [{ required: true, message: '请选择品种', trigger: 'change' }],
  lambingDate: [{ required: true, message: '请选择产羔日期', trigger: 'change' }],
  liveLambs: [{ required: true, message: '请输入活羔数', trigger: 'blur' }]
}

const breedingRules: FormRules = {
  femaleSheepId: [{ required: true, message: '请输入母羊编号', trigger: 'blur' }],
  maleSheepId: [{ required: true, message: '请输入公羊编号', trigger: 'blur' }],
  breedingDate: [{ required: true, message: '请选择配种日期', trigger: 'change' }],
  breedingMethod: [{ required: true, message: '请选择配种方式', trigger: 'change' }]
}

const productionRules: FormRules = {
  sheepId: [{ required: true, message: '请输入羊只编号', trigger: 'blur' }],
  recordType: [{ required: true, message: '请选择记录类型', trigger: 'change' }],
  recordDate: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  value: [{ required: true, message: '请输入数值', trigger: 'blur' }]
}

// 记录列表数据
const lambingRecords = ref([])
const breedingRecords = ref([])
const productionRecords = ref([])

// 处理标签页点击
const handleTabClick = (tab: any) => {
  // 可以在这里加载对应标签页的数据
}

// 提交产羔记录
const submitLambingRecord = async () => {
  if (!lambingFormRef.value) return
  
  try {
    await lambingFormRef.value.validate()
    
    // 转换表单数据为API期望的格式
    const apiData = {
      eweId: lambingForm.sheepId,
      lambingDate: lambingForm.lambingDate,
      lambCount: lambingForm.liveLambs + lambingForm.weakLambs,
      survivalRate: lambingForm.liveLambs / (lambingForm.liveLambs + lambingForm.weakLambs + lambingForm.stillbirth + lambingForm.deformed),
      notes: lambingForm.notes
    }
    
    await apiStore.addLambingRecord(apiData)
    resetLambingForm()
    refreshLambingRecords()
  } catch (error) {
    console.error('提交产羔记录失败:', error)
  }
}

// 提交配种记录
const submitBreedingRecord = async () => {
  if (!breedingFormRef.value) return
  
  try {
    await breedingFormRef.value.validate()
    
    // 转换表单数据为API期望的格式
    const apiData = {
      eweId: breedingForm.femaleSheepId,
      ramId: breedingForm.maleSheepId,
      breedingDate: breedingForm.breedingDate,
      pregnancyDays: breedingForm.expectedFarrowingDate ? 
        Math.floor((new Date(breedingForm.expectedFarrowingDate).getTime() - new Date(breedingForm.breedingDate).getTime()) / (1000 * 60 * 60 * 24)) : 
        undefined,
      success: breedingForm.breedingStatus === '成功',
      notes: breedingForm.notes
    }
    
    await apiStore.addBreedingRecord(apiData)
    resetBreedingForm()
    refreshBreedingRecords()
  } catch (error) {
    console.error('提交配种记录失败:', error)
  }
}

// 提交生产记录
const submitProductionRecord = async () => {
  if (!productionFormRef.value) return
  
  try {
    await productionFormRef.value.validate()
    
    // 转换表单数据为API期望的格式
    const apiData = {
      eweId: productionForm.sheepId,
      produceDate: productionForm.recordDate,
      produceCount: parseFloat(productionForm.value) || 0,
      issues: productionForm.recordType === '异常' ? productionForm.notes : undefined,
      notes: productionForm.notes
    }
    
    await apiStore.addProductionRecord(apiData)
    resetProductionForm()
    refreshProductionRecords()
  } catch (error) {
    console.error('提交生产记录失败:', error)
  }
}

// 重置表单
const resetLambingForm = () => {
  if (lambingFormRef.value) {
    lambingFormRef.value.resetFields()
  }
}

const resetBreedingForm = () => {
  if (breedingFormRef.value) {
    breedingFormRef.value.resetFields()
  }
}

const resetProductionForm = () => {
  if (productionFormRef.value) {
    productionFormRef.value.resetFields()
  }
}

// 刷新记录列表
const refreshLambingRecords = async () => {
  try {
    // 这里可以调用API获取产羔记录列表
    ElMessage.success('产羔记录列表已刷新')
  } catch (error) {
    console.error('刷新产羔记录失败:', error)
  }
}

const refreshBreedingRecords = async () => {
  try {
    // 这里可以调用API获取配种记录列表
    ElMessage.success('配种记录列表已刷新')
  } catch (error) {
    console.error('刷新配种记录失败:', error)
  }
}

const refreshProductionRecords = async () => {
  try {
    // 这里可以调用API获取生产记录列表
    ElMessage.success('生产记录列表已刷新')
  } catch (error) {
    console.error('刷新生产记录失败:', error)
  }
}

// 编辑记录
const editLambingRecord = (record: any) => {
  ElMessage.info('编辑功能开发中...')
}

const editBreedingRecord = (record: any) => {
  ElMessage.info('编辑功能开发中...')
}

const editProductionRecord = (record: any) => {
  ElMessage.info('编辑功能开发中...')
}

// 删除记录
const deleteLambingRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条产羔记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里调用删除API
    ElMessage.success('删除成功')
    refreshLambingRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除产羔记录失败:', error)
    }
  }
}

const deleteBreedingRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条配种记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里调用删除API
    ElMessage.success('删除成功')
    refreshBreedingRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配种记录失败:', error)
    }
  }
}

const deleteProductionRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条生产记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里调用删除API
    ElMessage.success('删除成功')
    refreshProductionRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除生产记录失败:', error)
    }
  }
}

onMounted(() => {
  // 初始化加载数据
  refreshLambingRecords()
  refreshBreedingRecords()
  refreshProductionRecords()
})
</script>

<style scoped>
.records-view {
  padding: 20px 0;
}

.tab-content {
  padding: 20px 0;
}

.form-section {
  margin-bottom: 20px;
}

.section-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.table-section {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.table-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.el-form {
  padding: 20px;
}

@media (max-width: 768px) {
  .el-form .el-row .el-col {
    margin-bottom: 15px;
  }
}
</style> 
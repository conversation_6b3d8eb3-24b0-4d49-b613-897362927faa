using MediatR;
using Summary.Domain.Entities;
using Summary.ErrorCode;

namespace Summary.API.Write.Application.command
{
    /// <summary>
    /// 添加产羔记录命令
    /// </summary>
    public class AddLambingRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 母羊ID
        /// </summary>
        public string EweId { get; set; } = string.Empty;
        
        /// <summary>
        /// 产羔日期
        /// </summary>
        public DateTime LambingDate { get; set; }
        
        /// <summary>
        /// 产羔数量
        /// </summary>
        public int LambCount { get; set; }
        
        /// <summary>
        /// 存活率
        /// </summary>
        public float SurvivalRate { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 更新产羔记录命令
    /// </summary>
    public class UpdateLambingRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long RecordId { get; set; }
        
        /// <summary>
        /// 产羔日期
        /// </summary>
        public DateTime? LambingDate { get; set; }
        
        /// <summary>
        /// 产羔数量
        /// </summary>
        public int? LambCount { get; set; }
        
        /// <summary>
        /// 存活率
        /// </summary>
        public float? SurvivalRate { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 删除产羔记录命令
    /// </summary>
    public class DeleteLambingRecordCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long RecordId { get; set; }
    }
} 
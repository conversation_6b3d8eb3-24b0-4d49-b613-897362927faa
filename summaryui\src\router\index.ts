import { createRouter, createWebHistory } from 'vue-router'
import DashboardView from '../views/DashboardView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'dashboard',
      component: DashboardView,
      meta: { title: '羊场首页' }
    },
    {
      path: '/inventory',
      name: 'inventory',
      component: () => import('../views/InventoryView.vue'),
      meta: { title: '羊场存栏统计' }
    },
    {
      path: '/lambing',
      name: 'lambing',
      component: () => import('../views/LambingView.vue'),
      meta: { title: '产羔统计' }
    },
    {
      path: '/breeding',
      name: 'breeding',
      component: () => import('../views/BreedingView.vue'),
      meta: { title: '配种统计' }
    },
    {
      path: '/production',
      name: 'production',
      component: () => import('../views/ProductionView.vue'),
      meta: { title: '生产统计' }
    },
    {
      path: '/records',
      name: 'records',
      component: () => import('../views/RecordsView.vue'),
      meta: { title: '记录管理' }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { title: '关于系统' }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 智慧畜牧养殖管理平台`
  }
  next()
})

export default router

<template>
  <div class="about-view">
    <!-- 返回首页按钮 -->
    <BackToHome />
    
    <div class="dashboard-card">
      <div class="about-content">
        <h1>智慧农业-智慧畜牧养殖管理平台</h1>
        <p class="version">版本: v1.0.0</p>
        
        <div class="feature-section">
          <h2>系统功能</h2>
          <ul>
            <li>羊场存栏统计 - 实时监控羊场存栏情况</li>
            <li>产羔统计 - 详细记录和分析产羔数据</li>
            <li>配种统计 - 管理配种过程和成功率</li>
            <li>生产统计 - 跟踪生产指标和趋势</li>
            <li>记录管理 - 完整的养殖记录管理</li>
          </ul>
        </div>
        
        <div class="tech-section">
          <h2>技术架构</h2>
          <div class="tech-stack">
            <div class="tech-item">
              <h3>前端技术</h3>
              <ul>
                <li>Vue 3 - 渐进式JavaScript框架</li>
                <li>TypeScript - 类型安全的JavaScript</li>
                <li>Element Plus - Vue 3组件库</li>
                <li>ECharts - 数据可视化图表库</li>
                <li>Pinia - Vue状态管理</li>
              </ul>
            </div>
            <div class="tech-item">
              <h3>后端技术</h3>
              <ul>
                <li>.NET 8 - 跨平台开发框架</li>
                <li>C# - 面向对象编程语言</li>
                <li>Entity Framework - ORM框架</li>
                <li>MediatR - 中介者模式实现</li>
                <li>AutoMapper - 对象映射</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="contact-section">
          <h2>联系我们</h2>
          <p>如有问题或建议，请联系开发团队</p>
          <p>邮箱: <EMAIL></p>
          <p>电话: 400-123-4567</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BackToHome from '@/components/BackToHome.vue'
// 关于页面逻辑
</script>

<style scoped>
.about-view {
  padding: 20px 0;
}

.about-content {
  padding: 40px;
  text-align: center;
}

.about-content h1 {
  color: var(--el-color-primary);
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.version {
  color: #909399;
  font-size: 1.1rem;
  margin-bottom: 40px;
}

.feature-section,
.tech-section,
.contact-section {
  margin-bottom: 40px;
  text-align: left;
}

.feature-section h2,
.tech-section h2,
.contact-section h2 {
  color: #303133;
  font-size: 1.5rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.feature-section ul {
  list-style: none;
  padding: 0;
}

.feature-section li {
  padding: 10px 0;
  color: #606266;
  font-size: 1.1rem;
  border-bottom: 1px solid #f0f0f0;
}

.feature-section li:last-child {
  border-bottom: none;
}

.tech-stack {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.tech-item h3 {
  color: #303133;
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.tech-item ul {
  list-style: none;
  padding: 0;
}

.tech-item li {
  padding: 8px 0;
  color: #606266;
  font-size: 1rem;
}

.contact-section p {
  color: #606266;
  font-size: 1.1rem;
  margin: 10px 0;
}

@media (max-width: 768px) {
  .about-content {
    padding: 20px;
  }
  
  .about-content h1 {
    font-size: 2rem;
  }
  
  .tech-stack {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
</style>

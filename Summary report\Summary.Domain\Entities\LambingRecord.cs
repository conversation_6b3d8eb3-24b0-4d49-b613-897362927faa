using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Summary.Domain.Entities
{
    /// <summary>
    /// 产羔记录表
    /// </summary>
    [Table("lambing_records")]
    public class LambingRecord
    {
        /// <summary>
        /// 记录ID - 主键，唯一标识每条产羔记录
        /// </summary>
        [Key]
        [Column("record_id")]
        public long RecordId { get; set; }

        /// <summary>
        /// 母羊ID - 产羔的母羊标识
        /// </summary>
        [Column("ewe_id")]
        public string EweId { get; set; } = string.Empty;

        /// <summary>
        /// 产羔日期 - 母羊产羔的具体时间
        /// </summary>
        [Column("lambing_date")]
        public DateTime LambingDate { get; set; }

        /// <summary>
        /// 产羔数量 - 本次产羔的羊羔数量
        /// </summary>
        [Column("lamb_count")]
        public int LambCount { get; set; }

        /// <summary>
        /// 存活率 - 羊羔的存活比例，范围0-1，如0.8表示80%存活率
        /// </summary>
        [Column("survival_rate")]
        public float SurvivalRate { get; set; }

        // 导航属性
        /// <summary>
        /// 母羊信息 - 产羔记录关联的母羊详细信息
        /// </summary>
        [ForeignKey("EweId")]
        public virtual Sheep Ewe { get; set; } = null!;
    }
} 
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #f5f7fa;
  color: #303133;
}

/* 自定义Element Plus主题色 */
:root {
  --el-color-primary: #e74c3c;
  --el-color-success: #27ae60;
  --el-color-warning: #f39c12;
  --el-color-danger: #e74c3c;
  --el-color-info: #3498db;
}

/* 布局样式 */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.main-content {
  padding: 20px;
  min-height: calc(100vh - 60px);
}

/* 卡片样式 */
.dashboard-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* 统计数字样式 */
.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--el-color-primary);
  text-align: center;
  margin: 20px 0;
}

.stat-label {
  text-align: center;
  color: #606266;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

/* 图表容器样式 */
.chart-container {
  height: 400px;
  width: 100%;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #f8f9fa !important;
  color: #303133;
  font-weight: 600;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  border: none;
  border-radius: 8px;
  font-weight: 500;
}

.el-button--primary:hover {
  background: linear-gradient(45deg, #c0392b, #a93226);
  transform: translateY(-1px);
}

/* 标签页样式 */
.el-tabs__item {
  font-weight: 500;
  color: #606266;
}

.el-tabs__item.is-active {
  color: var(--el-color-primary);
  font-weight: 600;
}

.el-tabs__active-bar {
  background-color: var(--el-color-primary);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .dashboard-card {
    margin-bottom: 15px;
  }
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state .el-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

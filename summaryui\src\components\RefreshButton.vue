<template>
  <el-button 
    type="success" 
    :icon="Refresh" 
    @click="handleRefresh"
    class="refresh-btn"
    :size="size"
    :loading="loading"
  >
    刷新数据
  </el-button>
</template>

<script setup lang="ts">
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Props {
  size?: 'large' | 'default' | 'small'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  loading: false
})

const emit = defineEmits<{
  refresh: []
}>()

const handleRefresh = () => {
  emit('refresh')
  ElMessage.success('数据刷新成功')
}
</script>

<style scoped>
.refresh-btn {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .refresh-btn {
    top: 80px;
    right: 10px;
    font-size: 0.8rem;
    padding: 8px 12px;
  }
}
</style> 
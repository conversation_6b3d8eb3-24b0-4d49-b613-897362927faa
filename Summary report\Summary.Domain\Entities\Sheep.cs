using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Summary.Domain.Entities
{
    /// <summary>
    /// 羊只信息表
    /// </summary>
    [Table("sheep")]
    public class Sheep
    {
        /// <summary>
        /// 羊只ID - 主键，唯一标识每只羊
        /// </summary>
        [Key]
        [Column("sheep_id")]
        public string SheepId { get; set; } = string.Empty;

        /// <summary>
        ///                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     mmm
        /// 农场ID - 所属农场的标识
        /// </summary>
        [Column("farm_id")]
        public string FarmId { get; set; } = string.Empty;

        /// <summary>
        /// 羊只品种 - 如：小尾寒羊、杜泊羊、萨福克羊等
        /// </summary>
        [Column("breed")]
        public string Breed { get; set; } = string.Empty;

        /// <summary>
        /// 羊只性别 - 1: 公羊, 2: 母羊
        /// </summary>
        [Column("gender")]
        public string Gender { get; set; } = string.Empty;

        /// <summary>
        /// 出生日期 - 羊只的出生时间
        /// </summary>
        [Column("birth_date")]
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// 健康状态 - 如：Healthy(健康)、Sick(生病)、Pregnant(怀孕)、Recovering(康复中)
        /// </summary>
        [Column("health")]
        public string Health { get; set; } = string.Empty;

        /// <summary>
        /// 饲养员ID - 负责该羊只的饲养员标识
        /// </summary>
        [Column("keeper_id")]
        public string KeeperId { get; set; } = string.Empty;

        /// <summary>
        /// 位置信息 - GPS坐标点，格式：纬度,经度
        /// </summary>
        [Column("location")]
        public string? Location { get; set; }

        // 导航属性
        /// <summary>
        /// 产羔记录集合 - 该羊只的所有产羔记录
        /// </summary>
        public virtual ICollection<LambingRecord> LambingRecords { get; set; } = new List<LambingRecord>();
        
        /// <summary>
        /// 配种记录集合(作为母羊) - 该羊只作为母羊的配种记录
        /// </summary>
        public virtual ICollection<BreedingRecord> BreedingRecordsAsEwe { get; set; } = new List<BreedingRecord>();
        
        /// <summary>
        /// 配种记录集合(作为公羊) - 该羊只作为公羊的配种记录
        /// </summary>
        public virtual ICollection<BreedingRecord> BreedingRecordsAsRam { get; set; } = new List<BreedingRecord>();
        
        /// <summary>
        /// 生产记录集合 - 该羊只的所有生产记录
        /// </summary>
        public virtual ICollection<ProductionRecord> ProductionRecords { get; set; } = new List<ProductionRecord>();
    }
} 
<template>
  <el-button 
    type="primary" 
    :icon="House" 
    @click="goHome"
    class="back-to-home-btn"
    :size="size"
  >
    返回首页
  </el-button>
</template>

<script setup lang="ts">
import { House } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

interface Props {
  size?: 'large' | 'default' | 'small'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default'
})

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.back-to-home-btn {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.back-to-home-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .back-to-home-btn {
    top: 80px;
    right: 10px;
    font-size: 0.8rem;
    padding: 8px 12px;
  }
}
</style> 
// 前端数据类型定义 - 完全匹配后端数据结构

// ==================== 枚举定义 ====================

/**
 * 羊只性别枚举
 */
export enum SheepGender {
  Male = 1,    // 公羊
  Female = 2   // 母羊
}

/**
 * 羊只健康状态枚举
 */
export enum SheepHealthStatus {
  Healthy = 'Healthy',     // 健康
  Sick = 'Sick',          // 生病
  Pregnant = 'Pregnant',  // 怀孕
  Recovering = 'Recovering' // 康复中
}

/**
 * 配种成功状态枚举
 */
export enum BreedingSuccess {
  Failed = 0,   // 失败
  Success = 1   // 成功
}

/**
 * 报表导出格式枚举
 */
export enum ExportFormat {
  PDF = 'PDF',
  Excel = 'Excel',
  Word = 'Word'
}

// ==================== 实体类型定义 ====================

/**
 * 羊只信息实体
 */
export interface Sheep {
  sheepId: string;           // 羊只ID
  farmId: string;            // 农场ID
  breed: string;             // 羊只品种
  gender: string;            // 羊只性别 (1: 公羊, 2: 母羊)
  birthDate?: string;        // 出生日期
  health: string;            // 健康状态
  keeperId: string;          // 饲养员ID
  location?: string;         // 位置信息 (GPS坐标)
  
  // 导航属性
  lambingRecords?: LambingRecord[];      // 产羔记录集合
  breedingRecordsAsEwe?: BreedingRecord[];   // 配种记录集合(作为母羊)
  breedingRecordsAsRam?: BreedingRecord[];   // 配种记录集合(作为公羊)
  productionRecords?: ProductionRecord[];    // 生产记录集合
}

/**
 * 产羔记录实体
 */
export interface LambingRecord {
  recordId: number;          // 记录ID
  eweId: string;             // 母羊ID
  lambingDate: string;       // 产羔日期
  lambCount: number;         // 产羔数量
  survivalRate: number;      // 存活率 (0-1)
  
  // 导航属性
  ewe?: Sheep;               // 母羊信息
}

/**
 * 配种记录实体
 */
export interface BreedingRecord {
  recordId: number;          // 记录ID
  eweId: string;             // 母羊ID
  ramId: string;             // 公羊ID
  breedingDate: string;      // 配种日期
  pregnancyDays?: number;    // 怀孕天数
  success: boolean;          // 配种成功状态
  
  // 导航属性
  ewe?: Sheep;               // 母羊信息
  ram?: Sheep;               // 公羊信息
}

/**
 * 生产记录实体
 */
export interface ProductionRecord {
  recordId: number;          // 记录ID
  eweId: string;             // 母羊ID
  produceDate: string;       // 生产日期
  produceCount: number;      // 生产数量
  issues?: string;           // 问题记录
  
  // 导航属性
  ewe?: Sheep;               // 母羊信息
}

// ==================== API响应类型定义 ====================

/**
 * API统一响应格式
 */
export interface APIResult<T> {
  code: number;              // 返回码
  message: string;           // 消息
  token: string;             // 访问令牌
  refreshToken: string;      // 刷新令牌
  data: T;                   // 返回数据
}

/**
 * 分页结果格式
 */
export interface APIPaging<T> {
  totalCount: number;        // 总记录数
  pageCount: number;         // 总页数
  pageData: T[];             // 分页数据集合
}

// ==================== 统计数据类型定义 ====================

/**
 * 存栏统计数据类型
 */
export interface InventoryStatistics {
  totalSheep: number;        // 总存栏数
  breedStatistics: BreedStatistics[];    // 品种统计
  genderStatistics: GenderStatistics[];  // 性别统计
  ageStatistics: AgeStatistics[];        // 年龄统计
  queryPeriod: {
    startDate?: string;
    endDate?: string;
  };
}

/**
 * 品种统计
 */
export interface BreedStatistics {
  breed: string;             // 品种
  totalCount: number;        // 总数量
  maleCount: number;         // 公羊数量
  femaleCount: number;       // 母羊数量
  healthyCount: number;      // 健康数量
  sickCount: number;         // 生病数量
  pregnantCount: number;     // 怀孕数量
}

/**
 * 性别统计
 */
export interface GenderStatistics {
  gender: string;            // 性别
  count: number;             // 数量
}

/**
 * 年龄统计
 */
export interface AgeStatistics {
  ageGroup: string;          // 年龄组
  count: number;             // 数量
}

/**
 * 产羔统计数据类型
 */
export interface LambingStatistics {
  summary: {
    totalLambingRecords: number;     // 总产羔记录数
    totalLambs: number;              // 总羔羊数
    averageLambsPerLambing: number;  // 平均每胎产羔数
    averageSurvivalRate: number;     // 平均存活率
  };
  monthlyStatistics: MonthlyLambingStatistics[];  // 月度统计
  breedStatistics: BreedLambingStatistics[];      // 品种统计
  topEweStatistics: TopEweStatistics[];           // 优秀母羊统计
  queryParameters: {
    farmId: string;
    startDate?: string;
    endDate?: string;
    breed?: string;
  };
}

/**
 * 月度产羔统计
 */
export interface MonthlyLambingStatistics {
  year: number;              // 年份
  month: number;             // 月份
  monthName: string;         // 月份名称
  totalLambing: number;      // 总产羔数
  totalLambs: number;        // 总羔羊数
  averageLambsPerLambing: number;  // 平均每胎产羔数
  averageSurvivalRate: number;     // 平均存活率
}

/**
 * 品种产羔统计
 */
export interface BreedLambingStatistics {
  breed: string;             // 品种
  totalLambing: number;      // 总产羔数
  totalLambs: number;        // 总羔羊数
  averageLambsPerLambing: number;  // 平均每胎产羔数
  averageSurvivalRate: number;     // 平均存活率
}

/**
 * 优秀母羊统计
 */
export interface TopEweStatistics {
  eweId: string;             // 母羊ID
  eweBreed: string;          // 母羊品种
  totalLambing: number;      // 总产羔数
  totalLambs: number;        // 总羔羊数
  averageLambsPerLambing: number;  // 平均每胎产羔数
  lastLambingDate: string;   // 最后产羔日期
}

/**
 * 配种统计数据类型
 */
export interface BreedingStatistics {
  summary: {
    totalBreedingRecords: number;    // 总配种记录数
    successCount: number;            // 成功数量
    successRate: number;             // 成功率
    averagePregnancyDays: number;    // 平均怀孕天数
  };
  monthlyStatistics: MonthlyBreedingStatistics[];  // 月度统计
  breedStatistics: BreedBreedingStatistics[];      // 品种统计
  topRamStatistics: TopRamStatistics[];            // 优秀公羊统计
  topEweStatistics: TopBreedingEweStatistics[];    // 优秀母羊统计
  queryParameters: {
    farmId: string;
    startDate?: string;
    endDate?: string;
    breed?: string;
  };
}

/**
 * 月度配种统计
 */
export interface MonthlyBreedingStatistics {
  year: number;              // 年份
  month: number;             // 月份
  totalBreeding: number;     // 总配种数
  successCount: number;      // 成功数量
  successRate: number;       // 成功率
}

/**
 * 品种配种统计
 */
export interface BreedBreedingStatistics {
  breed: string;             // 品种
  totalBreeding: number;     // 总配种数
  successCount: number;      // 成功数量
  successRate: number;       // 成功率
}

/**
 * 优秀公羊统计
 */
export interface TopRamStatistics {
  ramId: string;             // 公羊ID
  ramBreed: string;          // 公羊品种
  totalBreeding: number;     // 总配种数
  successCount: number;      // 成功数量
  successRate: number;       // 成功率
  lastBreedingDate: string;  // 最后配种日期
}

/**
 * 优秀配种母羊统计
 */
export interface TopBreedingEweStatistics {
  eweId: string;             // 母羊ID
  eweBreed: string;          // 母羊品种
  totalBreeding: number;     // 总配种数
  successCount: number;      // 成功数量
  successRate: number;       // 成功率
  lastBreedingDate: string;  // 最后配种日期
}

/**
 * 生产统计数据类型
 */
export interface ProductionStatistics {
  summary: {
    totalProductionRecords: number;  // 总生产记录数
    totalBreedingRecords: number;    // 总配种记录数
    totalAbnormalRecords: number;    // 总异常记录数
    totalFarrowingRecords: number;   // 总分娩记录数
    totalWeaningRecords: number;     // 总断奶记录数
    totalFemaleSheep: number;        // 总母羊数
    totalLambs: number;              // 总羔羊数
  };
  monthlyStatistics: MonthlyProductionStatistics[];  // 月度统计
  distributionStatistics: DistributionStatistics;    // 分布统计
  weaningStatistics: WeaningStatistics;              // 断奶统计
  productionTableData: ProductionTableData[];        // 生产表格数据
}

/**
 * 月度生产统计
 */
export interface MonthlyProductionStatistics {
  year: number;              // 年份
  month: number;             // 月份
  totalProduction: number;   // 总生产量
  averageWeight: number;     // 平均重量
}

/**
 * 分布统计
 */
export interface DistributionStatistics {
  total: number;             // 总数
  departure: number;         // 离场数
  liveLambs: number;         // 活羔数
  deadLambs: number;         // 死羔数
}

/**
 * 断奶统计
 */
export interface WeaningStatistics {
  total: number;             // 总数
  departure: number;         // 离场数
  deadLambs: number;         // 死羔数
  totalWeight: number;       // 总重量
  averageWeight: number;     // 平均重量
}

/**
 * 生产表格数据
 */
export interface ProductionTableData {
  category: string;          // 类别
  reserveBreeding: number;   // 后备配种
  weaningBreeding: number;   // 断奶配种
  returnBreeding: number;    // 返情配种
  unbred: number;            // 未配种
  emptyBreeding: number;     // 空怀配种
  abortionBreeding: number;  // 流产配种
  otherBreeding: number;     // 其他配种
  totalBreeding: number;     // 总配种数
}

// ==================== 实时数据类型定义 ====================

/**
 * 实时存栏数据类型
 */
export interface RealTimeInventory {
  sheepId: string;           // 羊只ID
  breed: string;             // 品种
  gender: string;            // 性别
  health: string;            // 健康状态
  keeperId: string;          // 饲养员ID
  location?: string;         // 位置信息
  age?: number;              // 年龄
  lastUpdateTime?: string;   // 最后更新时间
}

/**
 * 羊只详情数据类型
 */
export interface SheepDetails {
  basicInfo: {
    sheepId: string;         // 羊只ID
    farmId: string;          // 农场ID
    breed: string;           // 品种
    gender: string;          // 性别
    birthDate?: string;      // 出生日期
    age?: number;            // 年龄
    health: string;          // 健康状态
    keeperId: string;        // 饲养员ID
    location?: string;       // 位置信息
  };
  statistics: {
    lambingCount: number;    // 产羔次数
    breedingCount: number;   // 配种次数
    productionCount: number; // 生产次数
  };
  recentRecords: {
    lastLambing?: LambingRecord;     // 最后产羔记录
    lastBreeding?: BreedingRecord;   // 最后配种记录
    lastProduction?: ProductionRecord; // 最后生产记录
  };
}

// ==================== 表单数据类型定义 ====================

/**
 * 添加产羔记录表单
 */
export interface AddLambingRecordForm {
  eweId: string;             // 母羊ID
  lambingDate: string;       // 产羔日期
  lambCount: number;         // 产羔数量
  survivalRate: number;      // 存活率
  notes?: string;            // 备注
}

/**
 * 添加配种记录表单
 */
export interface AddBreedingRecordForm {
  eweId: string;             // 母羊ID
  ramId: string;             // 公羊ID
  breedingDate: string;      // 配种日期
  pregnancyDays?: number;    // 怀孕天数
  success: boolean;          // 配种成功状态
  notes?: string;            // 备注
}

/**
 * 添加生产记录表单
 */
export interface AddProductionRecordForm {
  eweId: string;             // 母羊ID
  produceDate: string;       // 生产日期
  produceCount: number;      // 生产数量
  issues?: string;           // 问题记录
  notes?: string;            // 备注
}

// ==================== 查询参数类型定义 ====================

/**
 * 存栏统计查询参数
 */
export interface InventoryQueryParams {
  startDate?: string;        // 开始日期
  endDate?: string;          // 结束日期
  summaryType?: string;      // 统计类型
  sheepType?: string;        // 羊只类型
  queryCondition?: string;   // 查询条件
  queryValue?: string;       // 查询值
}

/**
 * 产羔统计查询参数
 */
export interface LambingQueryParams {
  startDate?: string;        // 开始日期
  endDate?: string;          // 结束日期
  breed?: string;            // 品种
  year?: string;             // 年份
}

/**
 * 配种统计查询参数
 */
export interface BreedingQueryParams {
  startDate?: string;        // 开始日期
  endDate?: string;          // 结束日期
  breed?: string;            // 品种
  year?: string;             // 年份
}

/**
 * 生产统计查询参数
 */
export interface ProductionQueryParams {
  startDate?: string;        // 开始日期
  endDate?: string;          // 结束日期
  breed?: string;            // 品种
  period?: string;           // 期间
  date?: string;             // 日期
}

// ==================== 工具类型定义 ====================

/**
 * 性别显示映射
 */
export const GenderDisplayMap: Record<string, string> = {
  '1': '公羊',
  '2': '母羊'
};

/**
 * 健康状态显示映射
 */
export const HealthDisplayMap: Record<string, string> = {
  'Healthy': '健康',
  'Sick': '生病',
  'Pregnant': '怀孕',
  'Recovering': '康复中'
};

/**
 * 品种颜色映射
 */
export const BreedColorMap: Record<string, string> = {
  '杜泊': '#3498db',
  '澳洲白': '#2ecc71',
  '萨福克': '#f1c40f',
  '杜湖': '#e74c3c',
  '澳湖': '#9b59b6'
}; 
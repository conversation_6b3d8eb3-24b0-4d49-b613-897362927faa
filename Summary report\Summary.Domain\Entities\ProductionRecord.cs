using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Summary.Domain.Entities
{
    /// <summary>
    /// 生产记录表
    /// </summary>
    [Table("production_records")]
    public class ProductionRecord
    {
        /// <summary>
        /// 记录ID - 主键，唯一标识每条生产记录
        /// </summary>
        [Key]
        [Column("record_id")]
        public long RecordId { get; set; }

        /// <summary>
        /// 母羊ID - 生产的母羊标识
        /// </summary>
        [Column("ewe_id")]
        public string EweId { get; set; } = string.Empty;

        /// <summary>
        /// 生产日期 - 母羊生产的具体时间
        /// </summary>
        [Column("produce_date")]
        public DateTime ProduceDate { get; set; }

        /// <summary>
        /// 生产数量 - 本次生产的数量（如羊毛重量、奶量等）
        /// </summary>
        [Column("produce_count")]
        public int ProduceCount { get; set; }

        /// <summary>
        /// 问题记录 - 生产过程中遇到的问题或异常情况
        /// </summary>
        [Column("issues")]
        public string? Issues { get; set; }

        // 导航属性
        /// <summary>
        /// 母羊信息 - 生产记录关联的母羊详细信息
        /// </summary>
        [ForeignKey("EweId")]
        public virtual Sheep Ewe { get; set; } = new Sheep();
    }
} 
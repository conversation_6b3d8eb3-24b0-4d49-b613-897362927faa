<template>
  <div class="app-container">
    <header class="main-header">
      <div class="header-left">
        <h1 class="app-title">智慧农业-智慧畜牧养殖管理平台</h1>
        <div class="backend-status">
          <el-tag 
            :type="backendStatus.available ? 'success' : 'danger'" 
            size="small"
          >
            <el-icon><Connection /></el-icon>
            后端服务: {{ backendStatus.available ? '已连接' : '未连接' }}
          </el-tag>
          <span class="port-info" v-if="backendStatus.port">
            (端口: {{ backendStatus.port }})
          </span>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="apiStore.toggleMockData()" :type="apiStore.useMockData ? 'warning' : 'info'">
          <el-icon><DataAnalysis /></el-icon>
          {{ apiStore.useMockData ? '模拟数据' : '真实API' }}
        </el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </header>
    
    <main class="main-content">
      <RouterView />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { Refresh, DataAnalysis, Connection } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useApiStore } from '@/stores/api'
import { checkBackendPorts, getAvailableBackendInfo } from '@/utils/portChecker'

const apiStore = useApiStore()

const backendStatus = ref({
  available: false,
  port: null as number | null
})

const refreshData = () => {
  ElMessage.success('数据刷新成功')
  // 这里可以触发全局数据刷新
  window.location.reload()
}

const checkBackendStatus = async () => {
  try {
    const portStatus = await checkBackendPorts()
    const portInfo = getAvailableBackendInfo()
    
    // 检查哪个端口可用
    if (portStatus[5251]) {
      backendStatus.value = { available: true, port: 5251 }
    } else if (portStatus[5110]) {
      backendStatus.value = { available: true, port: 5110 }
    } else {
      backendStatus.value = { available: false, port: null }
      ElMessage.warning('后端服务未启动，将使用模拟数据')
    }
  } catch (error) {
    backendStatus.value = { available: false, port: null }
    console.warn('检查后端状态失败:', error)
  }
}

onMounted(() => {
  checkBackendStatus()
})
</script>

<style scoped>
.app-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.backend-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.port-info {
  font-size: 0.8rem;
  color: #909399;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

@media (max-width: 768px) {
  .app-title {
    font-size: 1.2rem;
  }
  
  .main-header {
    padding: 0 15px;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .header-right {
    flex-direction: column;
    gap: 5px;
  }
  
  .header-right .el-button {
    font-size: 0.8rem;
    padding: 5px 10px;
  }
}
</style>

using MediatR;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.command
{
    /// <summary>
    /// 获取产羔统计命令
    /// </summary>
    public class GetLambingStatisticsCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 农场ID
        /// </summary>
        public string FarmId { get; set; } = string.Empty;
        
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
        
        /// <summary>
        /// 品种
        /// </summary>
        public string? Breed { get; set; }
    }

    /// <summary>
    /// 生成产羔报表命令
    /// </summary>
    public class GenerateLambingReportCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 农场ID
        /// </summary>
        public string FarmId { get; set; } = string.Empty;
        
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
        
        /// <summary>
        /// 报表格式 - Excel, PDF
        /// </summary>
        public string Format { get; set; } = "Excel";
    }
} 
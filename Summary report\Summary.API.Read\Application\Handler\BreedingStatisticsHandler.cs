using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Read.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.Handler
{
    /// <summary>
    /// 配种统计处理器
    /// </summary>
    public class BreedingStatisticsHandler : 
        IRequestHandler<GetBreedingStatisticsCommand, APIResult<object>>,
        IRequestHandler<GenerateBreedingReportCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public BreedingStatisticsHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取配种统计
        /// </summary>
        public async Task<APIResult<object>> Handle(GetBreedingStatisticsCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var query = _context.BreedingRecords
                    .Include(b => b.Ewe)
                    .Include(b => b.Ram)
                    .Where(b => b.Ewe.FarmId == request.FarmId);

                // 按时间段筛选
                if (request.StartDate.HasValue)
                {
                    query = query.Where(b => b.BreedingDate >= request.StartDate.Value);
                }
                if (request.EndDate.HasValue)
                {
                    query = query.Where(b => b.BreedingDate <= request.EndDate.Value);
                }

                // 按品种筛选
                if (!string.IsNullOrEmpty(request.Breed))
                {
                    query = query.Where(b => b.Ewe.Breed == request.Breed);
                }

                var breedingRecords = await query.ToListAsync(cancellationToken);

                // 按月份统计
                var monthlyStats = breedingRecords
                    .GroupBy(b => new { b.BreedingDate.Year, b.BreedingDate.Month })
                    .Select(g => new
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        MonthName = $"{g.Key.Year}-{g.Key.Month:D2}",
                        TotalBreeding = g.Count(),
                        SuccessCount = g.Count(b => b.Success),
                        SuccessRate = g.Count() > 0 ? (double)g.Count(b => b.Success) / g.Count() * 100 : 0,
                        AveragePregnancyDays = g.Where(b => b.PregnancyDays.HasValue).Average(b => b.PregnancyDays) ?? 0
                    })
                    .OrderBy(s => s.Year).ThenBy(s => s.Month)
                    .ToList();

                // 按品种统计
                var breedStats = breedingRecords
                    .GroupBy(b => b.Ewe.Breed)
                    .Select(g => new
                    {
                        Breed = g.Key,
                        TotalBreeding = g.Count(),
                        SuccessCount = g.Count(b => b.Success),
                        SuccessRate = g.Count() > 0 ? (double)g.Count(b => b.Success) / g.Count() * 100 : 0,
                        AveragePregnancyDays = g.Where(b => b.PregnancyDays.HasValue).Average(b => b.PregnancyDays) ?? 0
                    })
                    .ToList();

                // 按公羊统计
                var ramStats = breedingRecords
                    .GroupBy(b => b.RamId)
                    .Select(g => new
                    {
                        RamId = g.Key,
                        RamBreed = g.First().Ram.Breed,
                        TotalBreeding = g.Count(),
                        SuccessCount = g.Count(b => b.Success),
                        SuccessRate = g.Count() > 0 ? (double)g.Count(b => b.Success) / g.Count() * 100 : 0
                    })
                    .OrderByDescending(s => s.SuccessRate)
                    .Take(10) // 取前10名
                    .ToList();

                // 按母羊统计
                var eweStats = breedingRecords
                    .GroupBy(b => b.EweId)
                    .Select(g => new
                    {
                        EweId = g.Key,
                        EweBreed = g.First().Ewe.Breed,
                        TotalBreeding = g.Count(),
                        SuccessCount = g.Count(b => b.Success),
                        SuccessRate = g.Count() > 0 ? (double)g.Count(b => b.Success) / g.Count() * 100 : 0,
                        LastBreedingDate = g.Max(b => b.BreedingDate)
                    })
                    .OrderByDescending(s => s.SuccessRate)
                    .Take(10) // 取前10名
                    .ToList();

                var data = new
                {
                    Summary = new
                    {
                        TotalBreedingRecords = breedingRecords.Count,
                        SuccessCount = breedingRecords.Count(b => b.Success),
                        SuccessRate = breedingRecords.Count > 0 ? (double)breedingRecords.Count(b => b.Success) / breedingRecords.Count * 100 : 0,
                        AveragePregnancyDays = breedingRecords.Where(b => b.PregnancyDays.HasValue).Average(b => b.PregnancyDays) ?? 0
                    },
                    MonthlyStatistics = monthlyStats,
                    BreedStatistics = breedStats,
                    TopRamStatistics = ramStats,
                    TopEweStatistics = eweStats,
                    QueryParameters = new
                    {
                        request.FarmId,
                        request.StartDate,
                        request.EndDate,
                        request.Breed
                    }
                };

                result.Code = ResultCode.Success;
                result.Message = "获取配种统计成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取配种统计失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 生成配种报表
        /// </summary>
        public async Task<APIResult<object>> Handle(GenerateBreedingReportCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var statisticsCommand = new GetBreedingStatisticsCommand
                {
                    FarmId = request.FarmId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate
                };

                var statisticsResult = await Handle(statisticsCommand, cancellationToken);
                
                var data = new
                {
                    ReportType = "配种统计报表",
                    Format = request.Format,
                    GeneratedTime = DateTime.Now,
                    FarmId = request.FarmId,
                    Statistics = statisticsResult.Data,
                    ExportUrl = $"/api/reports/breeding/{request.FarmId}?format={request.Format.ToLower()}"
                };

                result.Code = ResultCode.Success;
                result.Message = "生成配种报表成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"生成配种报表失败: {ex.Message}";
            }

            return result;
        }
    }
} 
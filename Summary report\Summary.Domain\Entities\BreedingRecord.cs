using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Summary.Domain.Entities
{
    /// <summary>
    /// 配种记录表
    /// </summary>
    [Table("breeding_records")]
    public class BreedingRecord
    {
        /// <summary>
        /// 记录ID - 主键，唯一标识每条配种记录
        /// </summary>
        [Key]
        [Column("record_id")]
        public long RecordId { get; set; }

        /// <summary>
        /// 母羊ID - 配种的母羊标识
        /// </summary>
        [Column("ewe_id")]
        public string EweId { get; set; } = string.Empty;

        /// <summary>
        /// 公羊ID - 配种的公羊标识
        /// </summary>
        [Column("ram_id")]
        public string RamId { get; set; } = string.Empty;

        /// <summary>
        /// 配种日期 - 配种的具体时间
        /// </summary>
        [Column("breeding_date")]
        public DateTime BreedingDate { get; set; }

        /// <summary>
        /// 怀孕天数 - 从配种到产羔的天数，通常为150天左右
        /// </summary>
        [Column("pregnancy_days")]
        public int? PregnancyDays { get; set; }

        /// <summary>
        /// 配种成功状态 - true: 成功, false: 失败
        /// </summary>
        [Column("success")]
        public bool Success { get; set; }

        // 导航属性
        /// <summary>
        /// 母羊信息 - 配种记录关联的母羊详细信息
        /// </summary>
        [ForeignKey("EweId")]
        public virtual Sheep Ewe { get; set; } = null!;

        /// <summary>
        /// 公羊信息 - 配种记录关联的公羊详细信息
        /// </summary>
        [ForeignKey("RamId")]
        public virtual Sheep Ram { get; set; } = null!;
    }
} 
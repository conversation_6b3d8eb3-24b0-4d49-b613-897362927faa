using MediatR;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.command
{
    /// <summary>
    /// 获取所有羊只信息命令
    /// </summary>
    public class GetSheepCommand : IRequest<APIResult<object>>
    {
        // 可以添加分页参数等
    }

    /// <summary>
    /// 根据ID获取羊只信息命令
    /// </summary>
    public class GetSheepByIdCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 羊只ID
        /// </summary>
        public string SheepId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 根据农场ID获取羊只列表命令
    /// </summary>
    public class GetSheepByFarmCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 农场ID
        /// </summary>
        public string FarmId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 根据品种获取羊只列表命令
    /// </summary>
    public class GetSheepByBreedCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 品种
        /// </summary>
        public string Breed { get; set; } = string.Empty;
    }


} 
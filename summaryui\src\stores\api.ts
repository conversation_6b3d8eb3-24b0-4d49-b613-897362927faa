import { defineStore } from 'pinia'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { generateMockData } from '@/utils/mockData'
import { getApiConfig } from '@/config/api'
import { transformBackendData } from '@/utils/dataTransform'
import type {
  APIResult,
  InventoryStatistics,
  LambingStatistics,
  BreedingStatistics,
  ProductionStatistics,
  RealTimeInventory,
  Sheep,
  AddLambingRecordForm,
  AddBreedingRecordForm,
  AddProductionRecordForm,
  InventoryQueryParams,
  LambingQueryParams,
  BreedingQueryParams,
  ProductionQueryParams
} from '@/types'

// 创建axios实例
const apiConfig = getApiConfig()
const api = axios.create({
  baseURL: apiConfig.baseURL,
  timeout: apiConfig.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const apiResult: APIResult<any> = response.data
    if (apiResult.code === 200) {
      return apiResult
    } else {
      ElMessage.error(apiResult.message || '请求失败')
      return Promise.reject(new Error(apiResult.message))
    }
  },
  (error) => {
    ElMessage.error(error.response?.data?.message || '网络请求失败')
    return Promise.reject(error)
  }
)

export const useApiStore = defineStore('api', {
  state: () => ({
    loading: false,
    farmId: 'default-farm', // 默认农场ID
    useMockData: false // 是否使用模拟数据
  }),

  actions: {
    // 设置农场ID
    setFarmId(farmId: string) {
      this.farmId = farmId
    },

    // 切换模拟数据模式
    toggleMockData() {
      this.useMockData = !this.useMockData
      ElMessage.info(this.useMockData ? '已切换到模拟数据模式' : '已切换到真实API模式')
    },

    // 羊场存栏统计
    async getInventoryStatistics(params: InventoryQueryParams): Promise<APIResult<InventoryStatistics>> {
      this.loading = true
      try {
        if (this.useMockData) {
          // 使用模拟数据
          await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟
          return {
            code: 200,
            message: '获取羊场存栏统计成功',
            token: '',
            refreshToken: '',
            data: generateMockData.inventoryStatistics()
          }
        }
        
        const response = await api.get(`/statistics/inventory/${this.farmId}`, { params })
        // 转换后端数据格式
        response.data = transformBackendData.inventoryStatistics(response.data)
        return response
      } catch (error) {
        // API调用失败时使用模拟数据
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        return {
          code: 200,
          message: '获取羊场存栏统计成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: generateMockData.inventoryStatistics()
        }
      } finally {
        this.loading = false
      }
    },

    // 羊场实时存栏
    async getRealTimeInventory(displayMode = 'list'): Promise<APIResult<RealTimeInventory[]>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 500))
          return {
            code: 200,
            message: '获取羊场实时存栏成功',
            token: '',
            refreshToken: '',
            data: generateMockData.realTimeInventory()
          }
        }
        
        const response = await api.get(`/statistics/realtime/${this.farmId}`, {
          params: { displayMode }
        })
        // 转换后端数据格式
        response.data = transformBackendData.realTimeInventory(response.data)
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        return {
          code: 200,
          message: '获取羊场实时存栏成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: generateMockData.realTimeInventory()
        }
      } finally {
        this.loading = false
      }
    },

    // 产羔统计
    async getLambingStatistics(params: LambingQueryParams): Promise<APIResult<LambingStatistics>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 500))
          return {
            code: 200,
            message: '获取产羔统计成功',
            token: '',
            refreshToken: '',
            data: generateMockData.lambingStatistics()
          }
        }
        
        const response = await api.get(`/statistics/lambing/${this.farmId}`, { params })
        // 转换后端数据格式
        response.data = transformBackendData.lambingStatistics(response.data)
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        return {
          code: 200,
          message: '获取产羔统计成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: generateMockData.lambingStatistics()
        }
      } finally {
        this.loading = false
      }
    },

    // 配种统计
    async getBreedingStatistics(params: BreedingQueryParams): Promise<APIResult<BreedingStatistics>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 500))
          return {
            code: 200,
            message: '获取配种统计成功',
            token: '',
            refreshToken: '',
            data: generateMockData.breedingStatistics()
          }
        }
        
        const response = await api.get(`/statistics/breeding/${this.farmId}`, { params })
        // 转换后端数据格式
        response.data = transformBackendData.breedingStatistics(response.data)
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        return {
          code: 200,
          message: '获取配种统计成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: generateMockData.breedingStatistics()
        }
      } finally {
        this.loading = false
      }
    },

    // 生产统计
    async getProductionStatistics(params: ProductionQueryParams): Promise<APIResult<ProductionStatistics>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 500))
          return {
            code: 200,
            message: '获取生产统计成功',
            token: '',
            refreshToken: '',
            data: generateMockData.productionStatistics()
          }
        }
        
        const response = await api.get(`/statistics/production/${this.farmId}`, { params })
        // 转换后端数据格式
        response.data = transformBackendData.productionStatistics(response.data)
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        return {
          code: 200,
          message: '获取生产统计成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: generateMockData.productionStatistics()
        }
      } finally {
        this.loading = false
      }
    },

    // 添加产羔记录
    async addLambingRecord(data: AddLambingRecordForm): Promise<APIResult<{ recordId: number }>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 500))
          ElMessage.success('产羔记录添加成功（模拟数据）')
          return {
            code: 200,
            message: '产羔记录添加成功',
            token: '',
            refreshToken: '',
            data: { recordId: Date.now() }
          }
        }
        
        const response = await api.post('/records/lambing', data)
        ElMessage.success('产羔记录添加成功')
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        ElMessage.success('产羔记录添加成功（模拟数据）')
        return {
          code: 200,
          message: '产羔记录添加成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: { recordId: Date.now() }
        }
      } finally {
        this.loading = false
      }
    },

    // 添加配种记录
    async addBreedingRecord(data: AddBreedingRecordForm): Promise<APIResult<{ recordId: number }>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 500))
          ElMessage.success('配种记录添加成功（模拟数据）')
          return {
            code: 200,
            message: '配种记录添加成功',
            token: '',
            refreshToken: '',
            data: { recordId: Date.now() }
          }
        }
        
        const response = await api.post('/records/breeding', data)
        ElMessage.success('配种记录添加成功')
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        ElMessage.success('配种记录添加成功（模拟数据）')
        return {
          code: 200,
          message: '配种记录添加成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: { recordId: Date.now() }
        }
      } finally {
        this.loading = false
      }
    },

    // 添加生产记录
    async addProductionRecord(data: AddProductionRecordForm): Promise<APIResult<{ recordId: number }>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 500))
          ElMessage.success('生产记录添加成功（模拟数据）')
          return {
            code: 200,
            message: '生产记录添加成功',
            token: '',
            refreshToken: '',
            data: { recordId: Date.now() }
          }
        }
        
        const response = await api.post('/records/production', data)
        ElMessage.success('生产记录添加成功')
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        ElMessage.success('生产记录添加成功（模拟数据）')
        return {
          code: 200,
          message: '生产记录添加成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: { recordId: Date.now() }
        }
      } finally {
        this.loading = false
      }
    },

    // 获取羊只信息
    async getSheep(params?: any): Promise<APIResult<Sheep[]>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 500))
          return {
            code: 200,
            message: '获取羊只信息成功',
            token: '',
            refreshToken: '',
            data: generateMockData.realTimeInventory()
          }
        }
        
        const response = await api.get('/sheep', { params })
        // 转换后端数据格式
        response.data = response.data.map((item: any) => transformBackendData.sheep(item))
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 300))
        return {
          code: 200,
          message: '获取羊只信息成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: generateMockData.realTimeInventory()
        }
      } finally {
        this.loading = false
      }
    },

    // 生成报表
    async generateReport(type: string, params: any): Promise<APIResult<{ reportUrl: string }>> {
      this.loading = true
      try {
        if (this.useMockData) {
          await new Promise(resolve => setTimeout(resolve, 1000))
          ElMessage.success('报表生成成功（模拟数据）')
          return {
            code: 200,
            message: '报表生成成功',
            token: '',
            refreshToken: '',
            data: { reportUrl: '/mock-report.xlsx' }
          }
        }
        
        const response = await api.get(`/statistics/${type}/${this.farmId}/report`, { params })
        ElMessage.success('报表生成成功')
        return response
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        ElMessage.warning('API连接失败，使用模拟数据')
        await new Promise(resolve => setTimeout(resolve, 500))
        ElMessage.success('报表生成成功（模拟数据）')
        return {
          code: 200,
          message: '报表生成成功（模拟数据）',
          token: '',
          refreshToken: '',
          data: { reportUrl: '/mock-report.xlsx' }
        }
      } finally {
        this.loading = false
      }
    }
  }
}) 
using Microsoft.AspNetCore.Mvc;
using Summary.API.Read.Application.command;
using Summary.ErrorCode;
using MediatR;

namespace Summary.API.Read.Controllers
{
    /// <summary>
    /// 羊只控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SheepController : ControllerBase
    {
        private readonly IMediator _mediator;

        public SheepController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 获取所有羊只信息
        /// </summary>
        [HttpGet]
        public async Task<APIResult<object>> GetSheep()
        {
            var request = new GetSheepCommand();
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 根据ID获取羊只信息
        /// </summary>
        [HttpGet("{id}")]
        public async Task<APIResult<object>> GetSheep(string id)
        {
            var request = new GetSheepByIdCommand
            {
                SheepId = id
            };
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 根据农场ID获取羊只列表
        /// </summary>
        [HttpGet("farm/{farmId}")]
        public async Task<APIResult<object>> GetSheepByFarm(string farmId)
        {
            var request = new GetSheepByFarmCommand
            {
                FarmId = farmId
            };
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 根据品种获取羊只列表
        /// </summary>
        [HttpGet("breed/{breed}")]
        public async Task<APIResult<object>> GetSheepByBreed(string breed)
        {
            var request = new GetSheepByBreedCommand
            {
                Breed = breed
            };
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 获取羊只的完整信息（包括相关记录）
        /// </summary>
        [HttpGet("{id}/details")]
        public async Task<APIResult<object>> GetSheepDetails(string id)
        {
            // 使用GetSheepByIdCommand，因为GetSheepDetailsCommand已经在RealTimeInventoryHandler中定义
            var request = new GetSheepByIdCommand
            {
                SheepId = id
            };
            return await _mediator.Send(request);
        }
    }
} 
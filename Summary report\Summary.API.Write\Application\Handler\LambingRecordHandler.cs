using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Write.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Write.Application.Handler
{
    /// <summary>
    /// 产羔记录处理器
    /// </summary>
    public class LambingRecordHandler : 
        IRequestHandler<AddLambingRecordCommand, APIResult<object>>,
        IRequestHandler<UpdateLambingRecordCommand, APIResult<object>>,
        IRequestHandler<DeleteLambingRecordCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public LambingRecordHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 添加产羔记录
        /// </summary>
        public async Task<APIResult<object>> Handle(AddLambingRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                // 验证母羊是否存在
                var ewe = await _context.Sheep.FindAsync(request.EweId);
                if (ewe == null)
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "母羊不存在";
                    return result;
                }

                // 验证母羊是否为母羊
                if (ewe.Gender != "2")
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "只能为母羊添加产羔记录";
                    return result;
                }

                var record = new LambingRecord
                {
                    EweId = request.EweId,
                    LambingDate = request.LambingDate,
                    LambCount = request.LambCount,
                    SurvivalRate = request.SurvivalRate
                };

                _context.LambingRecords.Add(record);
                await _context.SaveChangesAsync(cancellationToken);

                var data = new
                {
                    RecordId = record.RecordId,
                    record.EweId,
                    record.LambingDate,
                    record.LambCount,
                    record.SurvivalRate
                };

                result.Code = ResultCode.Success;
                result.Message = "产羔记录添加成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"添加产羔记录失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 更新产羔记录
        /// </summary>
        public async Task<APIResult<object>> Handle(UpdateLambingRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var record = await _context.LambingRecords.FindAsync(request.RecordId);
                if (record == null)
                {
                    result.Code = ResultCode.NotFound;
                    result.Message = "产羔记录不存在";
                    return result;
                }

                // 更新字段
                if (request.LambingDate.HasValue)
                    record.LambingDate = request.LambingDate.Value;
                if (request.LambCount.HasValue)
                    record.LambCount = request.LambCount.Value;
                if (request.SurvivalRate.HasValue)
                    record.SurvivalRate = request.SurvivalRate.Value;

                _context.LambingRecords.Update(record);
                await _context.SaveChangesAsync(cancellationToken);

                var data = new
                {
                    record.RecordId,
                    record.EweId,
                    record.LambingDate,
                    record.LambCount,
                    record.SurvivalRate
                };

                result.Code = ResultCode.Success;
                result.Message = "产羔记录更新成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"更新产羔记录失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 删除产羔记录
        /// </summary>
        public async Task<APIResult<object>> Handle(DeleteLambingRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var record = await _context.LambingRecords.FindAsync(request.RecordId);
                if (record == null)
                {
                    result.Code = ResultCode.NotFound;
                    result.Message = "产羔记录不存在";
                    return result;
                }

                _context.LambingRecords.Remove(record);
                await _context.SaveChangesAsync(cancellationToken);

                result.Code = ResultCode.Success;
                result.Message = "产羔记录删除成功";
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"删除产羔记录失败: {ex.Message}";
            }

            return result;
        }
    }
} 
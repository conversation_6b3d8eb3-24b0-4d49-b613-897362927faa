// API配置文件
export const API_CONFIG = {
  // 基础配置
  BASE_URL: '/api',
  TIMEOUT: 10000,
  
  // 后端服务端口
  READ_API_PORT: 5251,
  WRITE_API_PORT: 5110,
  
  // 完整的API端点
  READ_API_URL: `http://localhost:5251/api`,
  WRITE_API_URL: `http://localhost:5110/api`,
  
  // 开发环境代理配置
  DEV_PROXY: {
    target: 'http://localhost:5251',
    changeOrigin: true
  }
}

// 获取当前环境的API配置
export const getApiConfig = () => {
  const isDev = import.meta.env.DEV
  
  if (isDev) {
    // 开发环境使用代理
    return {
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT
    }
  } else {
    // 生产环境使用完整URL
    return {
      baseURL: API_CONFIG.READ_API_URL,
      timeout: API_CONFIG.TIMEOUT
    }
  }
} 
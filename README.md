# 智慧农业-智慧畜牧养殖管理平台

## 项目简介

智慧畜牧养殖管理平台是一个基于Vue 3 + .NET 8的现代化畜牧养殖管理系统，旨在为养殖场提供全面的数字化管理解决方案。

## 系统功能

### 🏠 羊场首页
- 实时存栏统计展示
- 种母存栏结构分析
- 种公存栏品种结构
- 快速操作导航

### 📊 羊场存栏统计
- 详细的存栏变动统计
- 转入转出数据追踪
- 高级查询功能
- 数据导出和打印

### 🐑 产羔统计
- 月产羔结构分析
- 窝均产活羔统计
- 胎次产羔结构
- 产羔哺乳情况
- 详细产羔记录

### 🔗 配种统计
- 配种成功率分析
- 种母状态分化
- 预估分娩率
- 配种记录管理

### 📈 生产统计
- 生产指标监控
- 分布统计
- 表格概况
- 趋势分析

### 📝 记录管理
- 产羔记录管理
- 配种记录管理
- 生产记录管理
- 数据增删改查

## 技术架构

### 前端技术栈
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript
- **Element Plus** - Vue 3组件库
- **ECharts** - 数据可视化图表库
- **Pinia** - Vue状态管理
- **Vue Router** - 前端路由
- **Axios** - HTTP客户端

### 后端技术栈
- **.NET 8** - 跨平台开发框架
- **C#** - 面向对象编程语言
- **Entity Framework Core** - ORM框架
- **MediatR** - 中介者模式实现
- **AutoMapper** - 对象映射
- **NLog** - 日志记录

## 项目结构

```
Summary/
├── Summary report/          # 后端项目
│   ├── Summary.API.Read/    # 读取API
│   ├── Summary.API.Write/   # 写入API
│   ├── Summary.Domain/      # 领域层
│   ├── Summary.Infrastructure/ # 基础设施层
│   └── Summary.ErrorCode/   # 错误码定义
└── summaryui/               # 前端项目
    ├── src/
    │   ├── views/           # 页面组件
    │   ├── components/      # 通用组件
    │   ├── stores/          # 状态管理
    │   ├── router/          # 路由配置
    │   └── assets/          # 静态资源
    └── package.json
```

## 快速开始

### 环境要求
- Node.js 20.19.0 或更高版本
- .NET 8 SDK
- SQL Server 或 SQLite

### 前端启动
```bash
cd summaryui
npm install
npm run dev
```

前端服务将在 http://localhost:5173 启动

### 后端启动
```bash
# 启动读取API
cd "Summary report/Summary.API.Read"
dotnet run

# 启动写入API
cd "Summary report/Summary.API.Write"
dotnet run
```

## API接口

### 统计相关接口
- `GET /api/statistics/inventory/{farmId}` - 获取存栏统计
- `GET /api/statistics/lambing/{farmId}` - 获取产羔统计
- `GET /api/statistics/breeding/{farmId}` - 获取配种统计
- `GET /api/statistics/production/{farmId}` - 获取生产统计

### 记录管理接口
- `POST /api/records/lambing` - 添加产羔记录
- `POST /api/records/breeding` - 添加配种记录
- `POST /api/records/production` - 添加生产记录

## 主要特性

### 🎨 现代化UI设计
- 响应式布局，支持移动端
- 毛玻璃效果和渐变背景
- 流畅的动画过渡
- 直观的数据可视化

### 📊 丰富的数据展示
- 多种图表类型（柱状图、折线图、饼图）
- 实时数据更新
- 交互式图表操作
- 数据导出功能

### 🔍 强大的查询功能
- 多条件组合查询
- 高级筛选功能
- 日期范围选择
- 实时搜索

### 📱 移动端适配
- 响应式设计
- 触摸友好的交互
- 移动端优化的布局
- 快速加载

## 开发指南

### 添加新页面
1. 在 `src/views/` 目录下创建新的Vue组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 在 `src/stores/api.ts` 中添加对应的API调用

### 添加新图表
1. 使用ECharts创建图表配置
2. 在组件中初始化图表
3. 监听窗口大小变化，自动调整图表尺寸

### 样式规范
- 使用Element Plus的设计规范
- 遵循BEM命名规范
- 使用CSS变量管理主题色
- 响应式设计优先

## 部署说明

### 前端部署
```bash
cd summaryui
npm run build
```

构建产物将生成在 `dist/` 目录中

### 后端部署
```bash
dotnet publish -c Release
```

发布文件将生成在 `bin/Release/net8.0/publish/` 目录中

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 邮箱: <EMAIL>
- 电话: 400-123-4567
- 项目地址: [GitHub Repository](https://github.com/your-repo/smart-farm)

---

**智慧农业-智慧畜牧养殖管理平台** - 让养殖管理更智能、更高效！ 
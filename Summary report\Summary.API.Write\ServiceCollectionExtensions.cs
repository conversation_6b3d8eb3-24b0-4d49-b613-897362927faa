﻿using Microsoft.EntityFrameworkCore;
using System.Reflection;
using Yitter.IdGenerator;
using System.Text.Json;
using System.Text.Json.Serialization;
using Summary.Infrastructure;

namespace Summary.API.Write
{
   
        public static class ServiceCollectionExtensions
        {
            public static WebApplicationBuilder Inject(this WebApplicationBuilder builder)
            {
                builder.Services.AddControllers()
                    .AddJsonOptions(options =>
                    {
                        // 配置JSON序列化选项来处理循环引用
                        options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                        options.JsonSerializerOptions.WriteIndented = true;
                    });
                // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
                builder.Services.AddEndpointsApiExplorer();
                //配置Swagger
                builder.Services.AddSwaggerGen(o =>
                {
                    var path = AppDomain.CurrentDomain.BaseDirectory + "Summary.API.Write.xml";
                    o.IncludeXmlComments(path, true);

                });
                //注册上下文EFCore上下文，开发环境下启用SQL日志记录
                builder.Services.AddDbContext<MyDbContext>(x =>
                {
                    x.UseMySql(builder.Configuration.GetConnectionString("MySQL"), new MySqlServerVersion("5.7"));
                });
                //注册中介者
                builder.Services.AddMediatR(x => x.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
                //注册雪花编号
                YitIdHelper.SetIdGenerator(new IdGeneratorOptions(1));
                //注入日志
                //注册AutoMapper
                //builder.Services.AddAutoMapper(typeof(MedicineOrderMappingProfile));

                //注册仓储
                builder.Services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
                builder.Services.AddHttpContextAccessor();


                builder.Services.AddAuthorization();

                return builder;
            }
        
    }
}

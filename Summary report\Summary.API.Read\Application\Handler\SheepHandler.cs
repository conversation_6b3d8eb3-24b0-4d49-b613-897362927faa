using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Read.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.Handler
{
    /// <summary>
    /// 羊只查询处理器
    /// </summary>
    public class SheepHandler : 
        IRequestHandler<GetSheepCommand, APIResult<object>>,
        IRequestHandler<GetSheepByIdCommand, APIResult<object>>,
        IRequestHandler<GetSheepByFarmCommand, APIResult<object>>,
        IRequestHandler<GetSheepByBreedCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public SheepHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取所有羊只信息
        /// </summary>
        public async Task<APIResult<object>> Handle(GetSheepCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var sheepList = await _context.Sheep
                    .ToListAsync(cancellationToken);

                result.Code = ResultCode.Success;
                result.Message = "获取所有羊只信息成功";
                result.Data = sheepList;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取所有羊只信息失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 根据ID获取羊只信息
        /// </summary>
        public async Task<APIResult<object>> Handle(GetSheepByIdCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var sheep = await _context.Sheep
                    .FirstOrDefaultAsync(s => s.SheepId == request.SheepId, cancellationToken);

                if (sheep == null)
                {
                    result.Code = ResultCode.NotFound;
                    result.Message = "羊只不存在";
                    return result;
                }

                result.Code = ResultCode.Success;
                result.Message = "获取羊只信息成功";
                result.Data = sheep;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取羊只信息失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 根据农场ID获取羊只列表
        /// </summary>
        public async Task<APIResult<object>> Handle(GetSheepByFarmCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var sheepList = await _context.Sheep
                    .Where(s => s.FarmId == request.FarmId)
                    .ToListAsync(cancellationToken);

                result.Code = ResultCode.Success;
                result.Message = "获取农场羊只列表成功";
                result.Data = sheepList;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取农场羊只列表失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 根据品种获取羊只列表
        /// </summary>
        public async Task<APIResult<object>> Handle(GetSheepByBreedCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var sheepList = await _context.Sheep
                    .Where(s => s.Breed == request.Breed)
                    .ToListAsync(cancellationToken);

                result.Code = ResultCode.Success;
                result.Message = "获取品种羊只列表成功";
                result.Data = sheepList;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取品种羊只列表失败: {ex.Message}";
            }

            return result;
        }


    }
} 
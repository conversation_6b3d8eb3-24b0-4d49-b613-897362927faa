using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;

namespace Summary.Infrastructure
{
    /// <summary>
    /// 数据库种子数据
    /// </summary>
    public static class DatabaseSeeder
    {
        /// <summary>
        /// 初始化数据库种子数据
        /// </summary>
        public static async Task SeedAsync(MyDbContext context)
        {
            // 清空现有数据并重新添加
            context.Sheep.RemoveRange(context.Sheep);
            context.LambingRecords.RemoveRange(context.LambingRecords);
            context.BreedingRecords.RemoveRange(context.BreedingRecords);
            context.ProductionRecords.RemoveRange(context.ProductionRecords);
            await context.SaveChangesAsync();

            // 添加羊只数据
            var sheepList = new List<Sheep>
            {
                // 杜泊品种
                new Sheep { SheepId = "S001", FarmId = "default-farm", Breed = "杜泊", Gender = "1", BirthDate = new DateTime(2022, 3, 15), Health = "Healthy", KeeperId = "K001" },
                new Sheep { SheepId = "S002", FarmId = "default-farm", Breed = "杜泊", Gender = "2", BirthDate = new DateTime(2022, 1, 20), Health = "Pregnant", KeeperId = "K001" },
                new Sheep { SheepId = "S003", FarmId = "default-farm", Breed = "杜泊", Gender = "2", BirthDate = new DateTime(2022, 5, 10), Health = "Healthy", KeeperId = "K001" },
                new Sheep { SheepId = "S004", FarmId = "default-farm", Breed = "杜泊", Gender = "1", BirthDate = new DateTime(2023, 2, 8), Health = "Healthy", KeeperId = "K002" },
                new Sheep { SheepId = "S005", FarmId = "default-farm", Breed = "杜泊", Gender = "2", BirthDate = new DateTime(2023, 4, 12), Health = "Pregnant", KeeperId = "K002" },
                
                // 澳洲白品种
                new Sheep { SheepId = "S006", FarmId = "default-farm", Breed = "澳洲白", Gender = "1", BirthDate = new DateTime(2022, 6, 25), Health = "Healthy", KeeperId = "K001" },
                new Sheep { SheepId = "S007", FarmId = "default-farm", Breed = "澳洲白", Gender = "2", BirthDate = new DateTime(2022, 8, 30), Health = "Healthy", KeeperId = "K001" },
                new Sheep { SheepId = "S008", FarmId = "default-farm", Breed = "澳洲白", Gender = "2", BirthDate = new DateTime(2023, 1, 15), Health = "Pregnant", KeeperId = "K002" },
                new Sheep { SheepId = "S009", FarmId = "default-farm", Breed = "澳洲白", Gender = "1", BirthDate = new DateTime(2023, 3, 22), Health = "Healthy", KeeperId = "K002" },
                
                // 萨福克品种
                new Sheep { SheepId = "S010", FarmId = "default-farm", Breed = "萨福克", Gender = "1", BirthDate = new DateTime(2022, 9, 5), Health = "Healthy", KeeperId = "K001" },
                new Sheep { SheepId = "S011", FarmId = "default-farm", Breed = "萨福克", Gender = "2", BirthDate = new DateTime(2022, 11, 18), Health = "Healthy", KeeperId = "K001" },
                new Sheep { SheepId = "S012", FarmId = "default-farm", Breed = "萨福克", Gender = "2", BirthDate = new DateTime(2023, 2, 28), Health = "Pregnant", KeeperId = "K002" },
                
                // 杜湖品种
                new Sheep { SheepId = "S013", FarmId = "default-farm", Breed = "杜湖", Gender = "1", BirthDate = new DateTime(2023, 5, 10), Health = "Healthy", KeeperId = "K002" },
                new Sheep { SheepId = "S014", FarmId = "default-farm", Breed = "杜湖", Gender = "2", BirthDate = new DateTime(2023, 6, 15), Health = "Healthy", KeeperId = "K002" },
                
                // 澳湖品种
                new Sheep { SheepId = "S015", FarmId = "default-farm", Breed = "澳湖", Gender = "1", BirthDate = new DateTime(2023, 7, 20), Health = "Healthy", KeeperId = "K001" },
                new Sheep { SheepId = "S016", FarmId = "default-farm", Breed = "澳湖", Gender = "2", BirthDate = new DateTime(2023, 8, 25), Health = "Healthy", KeeperId = "K001" }
            };

            await context.Sheep.AddRangeAsync(sheepList);

            // 添加产羔记录
            var lambingRecords = new List<LambingRecord>
            {
                new LambingRecord { RecordId = 1, EweId = "S002", LambingDate = new DateTime(2023, 12, 15), LambCount = 2, SurvivalRate = 1.0 },
                new LambingRecord { RecordId = 2, EweId = "S005", LambingDate = new DateTime(2023, 12, 10), LambCount = 1, SurvivalRate = 1.0 },
                new LambingRecord { RecordId = 3, EweId = "S008", LambingDate = new DateTime(2023, 12, 8), LambCount = 2, SurvivalRate = 0.5 },
                new LambingRecord { RecordId = 4, EweId = "S012", LambingDate = new DateTime(2023, 12, 5), LambCount = 1, SurvivalRate = 1.0 }
            };

            await context.LambingRecords.AddRangeAsync(lambingRecords);

            // 添加配种记录
            var breedingRecords = new List<BreedingRecord>
            {
                new BreedingRecord { RecordId = 1, EweId = "S003", RamId = "S001", BreedingDate = new DateTime(2023, 11, 15), Success = true },
                new BreedingRecord { RecordId = 2, EweId = "S007", RamId = "S006", BreedingDate = new DateTime(2023, 11, 20), Success = true },
                new BreedingRecord { RecordId = 3, EweId = "S011", RamId = "S010", BreedingDate = new DateTime(2023, 11, 25), Success = false },
                new BreedingRecord { RecordId = 4, EweId = "S014", RamId = "S013", BreedingDate = new DateTime(2023, 12, 1), Success = true }
            };

            await context.BreedingRecords.AddRangeAsync(breedingRecords);

            // 添加生产记录
            var productionRecords = new List<ProductionRecord>
            {
                new ProductionRecord { RecordId = 1, EweId = "S002", ProduceDate = new DateTime(2023, 12, 15), ProduceCount = 2 },
                new ProductionRecord { RecordId = 2, EweId = "S005", ProduceDate = new DateTime(2023, 12, 10), ProduceCount = 1 },
                new ProductionRecord { RecordId = 3, EweId = "S008", ProduceDate = new DateTime(2023, 12, 8), ProduceCount = 1 },
                new ProductionRecord { RecordId = 4, EweId = "S012", ProduceDate = new DateTime(2023, 12, 5), ProduceCount = 1 }
            };

            await context.ProductionRecords.AddRangeAsync(productionRecords);

            // 保存到数据库
            await context.SaveChangesAsync();
        }
    }
} 
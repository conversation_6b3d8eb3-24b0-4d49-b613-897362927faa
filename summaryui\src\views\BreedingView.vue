<template>
  <div class="breeding-view">
    <!-- 返回首页按钮 -->
    <BackToHome />
    
    <!-- 查询条件 -->
    <div class="dashboard-card query-section">
      <el-form :model="queryForm" inline>
        <el-form-item label="年份">
          <el-input v-model="queryForm.year" placeholder="请输入年份" style="width: 120px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="apiStore.loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 配种统计图表 -->
    <div class="dashboard-card chart-section">
      <div class="chart-container" ref="breedingChartRef"></div>
    </div>

    <!-- 种母状态分化 -->
    <div class="dashboard-card status-section">
      <div class="section-header">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="预估分娩率" name="estimated" />
          <el-tab-pane label="配种分娩率" name="breeding" />
          <el-tab-pane label="种母状态分化" name="status" />
        </el-tabs>
        
        <div class="filter-controls">
          <el-select v-model="selectedBreed" placeholder="请选择品种" style="width: 150px">
            <el-option label="全部品种" value="" />
            <el-option label="杜泊" value="杜泊" />
            <el-option label="澳洲白" value="澳洲白" />
            <el-option label="萨福克" value="萨福克" />
            <el-option label="杜湖" value="杜湖" />
            <el-option label="澳湖" value="澳湖" />
          </el-select>
        </div>
      </div>
      
      <div class="status-chart-container" ref="statusChartRef"></div>
    </div>

    <!-- 配种统计表格 -->
    <div class="dashboard-card table-section">
      <div class="table-header">
        <h3>配种统计详情</h3>
        <div class="table-actions">
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
        </div>
      </div>
      
      <el-table :data="breedingData" border stripe>
        <el-table-column prop="month" label="月份" width="100" />
        <el-table-column prop="breedingCount" label="配种种母数" width="120" />
        <el-table-column prop="farrowingCount" label="分娩种母数" width="120" />
        <el-table-column prop="breedingRate" label="配种分娩率" width="120">
          <template #default="scope">
            {{ scope.row.breedingRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="estimatedRate" label="预估分娩率" width="120">
          <template #default="scope">
            {{ scope.row.estimatedRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="breed" label="品种" width="100" />
        <el-table-column prop="notes" label="备注" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { useApiStore } from '@/stores/api'
import { Search, Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import BackToHome from '@/components/BackToHome.vue'

const apiStore = useApiStore()

// 响应式数据
const activeTab = ref('status')
const selectedBreed = ref('')
const queryForm = reactive({
  year: '2023'
})

// 配种数据
const breedingData = ref([
  {
    month: '一月',
    breedingCount: 0,
    farrowingCount: 0,
    breedingRate: 0,
    estimatedRate: 0,
    status: '待配种',
    breed: '杜泊',
    notes: ''
  },
  {
    month: '二月',
    breedingCount: 0,
    farrowingCount: 0,
    breedingRate: 0,
    estimatedRate: 0,
    status: '妊娠换料',
    breed: '澳洲白',
    notes: ''
  },
  {
    month: '三月',
    breedingCount: 0,
    farrowingCount: 0,
    breedingRate: 0,
    estimatedRate: 0,
    status: '待分娩',
    breed: '萨福克',
    notes: ''
  }
])

// 图表引用
const breedingChartRef = ref<HTMLElement>()
const statusChartRef = ref<HTMLElement>()

// 处理标签页点击
const handleTabClick = (tab: any) => {
  nextTick(() => {
    initStatusChart({})
  })
}

// 查询处理
const handleQuery = async () => {
  try {
    const params = {
      year: queryForm.year,
      breed: selectedBreed.value
    }
    
    const response = await apiStore.getBreedingStatistics(params)
    if (response && response.data) {
      updateChartData(response.data)
    }
  } catch (error) {
    console.error('查询失败:', error)
  }
}

// 更新图表数据
const updateChartData = (data: any) => {
  // 根据后端返回的数据更新图表
  if (data) {
    // 更新配种数据
    if (data.MonthlyStatistics) {
      breedingData.value = data.MonthlyStatistics.map((item: any) => ({
        month: `${item.Year}年${item.Month}月`,
        breedingCount: item.TotalBreeding,
        farrowingCount: item.SuccessCount,
        breedingRate: Math.round(item.SuccessRate * 100) / 100,
        estimatedRate: Math.round(item.SuccessRate * 100) / 100,
        status: item.SuccessRate > 80 ? '优秀' : item.SuccessRate > 60 ? '良好' : '待改进',
        breed: '综合',
        notes: ''
      }))
    }
    
    // 重新初始化图表
    nextTick(() => {
      initBreedingChart(data)
      initStatusChart(data)
    })
  }
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待配种': 'primary',
    '妊娠换料': 'warning',
    '待分娩': 'success',
    '待上产床': 'danger',
    '待产': 'info',
    '待次灯验': 'warning',
    '待次配': 'primary',
    '待转群': 'info',
    '待淘汰': 'danger',
    '空怀': 'info'
  }
  return statusMap[status] || 'info'
}

// 导出报表
const handleExport = async () => {
  try {
    const params = {
      year: queryForm.year,
      breed: selectedBreed.value,
      format: 'Excel'
    }
    
    await apiStore.generateReport('breeding', params)
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 初始化配种统计图表
const initBreedingChart = (data: any) => {
  if (!breedingChartRef.value) return
  
  const chart = echarts.init(breedingChartRef.value)
  
  // 根据真实数据构建图表数据
  const monthlyStats = data?.MonthlyStatistics || []
  const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
  
  const breedingData = months.map((month, index) => {
    const monthData = monthlyStats.find((item: any) => item.Month === index + 1)
    return monthData ? monthData.TotalBreeding : 0
  })
  
  const farrowingData = months.map((month, index) => {
    const monthData = monthlyStats.find((item: any) => item.Month === index + 1)
    return monthData ? monthData.SuccessCount : 0
  })
  
  const rateData = months.map((month, index) => {
    const monthData = monthlyStats.find((item: any) => item.Month === index + 1)
    return monthData ? monthData.SuccessRate : 0
  })
  
  const option = {
    title: {
      text: '配种统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((param: any) => {
          result += param.marker + param.seriesName + ': ' + param.value + '<br/>'
        })
        return result
      }
    },
    legend: {
      data: ['配种种母数', '分娩种母数', '配种分娩率'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '比率',
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '配种种母数',
        type: 'bar',
        data: breedingData,
        itemStyle: { color: '#5470c6' }
      },
      {
        name: '分娩种母数',
        type: 'bar',
        data: farrowingData,
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '配种分娩率',
        type: 'line',
        yAxisIndex: 1,
        data: rateData,
        itemStyle: { color: '#fc8452' },
        symbol: 'circle'
      }
    ]
  }
  chart.setOption(option)
}

// 初始化种母状态分化图表
const initStatusChart = (data: any) => {
  if (!statusChartRef.value) return
  
  const chart = echarts.init(statusChartRef.value)
  
  // 根据真实数据构建图表数据
  const breedStats = data?.BreedStatistics || []
  const summary = data?.Summary || {}
  
  // 构建种母状态数据
  const statusData = [
    { value: Math.round(summary.TotalBreedingRecords * 0.3), name: '待配种', itemStyle: { color: '#5470c6' } },
    { value: Math.round(summary.TotalBreedingRecords * 0.25), name: '妊娠换料', itemStyle: { color: '#fac858' } },
    { value: Math.round(summary.TotalBreedingRecords * 0.2), name: '种母待断奶', itemStyle: { color: '#91cc75' } },
    { value: Math.round(summary.TotalBreedingRecords * 0.15), name: '待分娩', itemStyle: { color: '#73c0de' } },
    { value: Math.round(summary.TotalBreedingRecords * 0.1), name: '其他', itemStyle: { color: '#ee6666' } }
  ].filter(item => item.value > 0)
  
  const option = {
    title: {
      text: '种母存栏结构',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}只 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: statusData.map((item: any) => item.name)
    },
    series: [
      {
        name: '种母存栏',
        type: 'pie',
        radius: '50%',
        center: ['60%', '50%'],
        data: statusData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 监听品种选择变化
watch(selectedBreed, () => {
  handleQuery()
})

onMounted(async () => {
  await handleQuery()
  
  await nextTick()
  initBreedingChart({})
  initStatusChart({})
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    const breedingChart = echarts.getInstanceByDom(breedingChartRef.value!)
    const statusChart = echarts.getInstanceByDom(statusChartRef.value!)
    breedingChart?.resize()
    statusChart?.resize()
  })
})
</script>

<style scoped>
.breeding-view {
  padding: 20px 0;
}

.query-section {
  margin-bottom: 20px;
  padding: 20px;
}

.chart-section {
  margin-bottom: 20px;
}

.status-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.filter-controls {
  display: flex;
  gap: 10px;
}

.status-chart-container {
  height: 400px;
  padding: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.table-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.chart-container {
  height: 400px;
  padding: 20px;
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .chart-container,
  .status-chart-container {
    height: 300px;
  }
}
</style> 
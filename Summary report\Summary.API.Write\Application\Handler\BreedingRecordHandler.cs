using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Write.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Write.Application.Handler
{
    /// <summary>
    /// 配种记录处理器
    /// </summary>
    public class BreedingRecordHandler : 
        IRequestHandler<AddBreedingRecordCommand, APIResult<object>>,
        IRequestHandler<UpdateBreedingRecordCommand, APIResult<object>>,
        IRequestHandler<DeleteBreedingRecordCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public BreedingRecordHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 添加配种记录
        /// </summary>
        public async Task<APIResult<object>> Handle(AddBreedingRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                // 验证母羊是否存在
                var ewe = await _context.Sheep.FindAsync(request.EweId);
                if (ewe == null)
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "母羊不存在";
                    return result;
                }

                // 验证公羊是否存在
                var ram = await _context.Sheep.FindAsync(request.RamId);
                if (ram == null)
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "公羊不存在";
                    return result;
                }

                // 验证性别
                if (ewe.Gender != "2")
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "EweId必须是母羊";
                    return result;
                }
                if (ram.Gender != "1")
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "RamId必须是公羊";
                    return result;
                }

                var record = new BreedingRecord
                {
                    EweId = request.EweId,
                    RamId = request.RamId,
                    BreedingDate = request.BreedingDate,
                    PregnancyDays = request.PregnancyDays,
                    Success = request.Success
                };

                _context.BreedingRecords.Add(record);
                await _context.SaveChangesAsync(cancellationToken);

                var data = new
                {
                    RecordId = record.RecordId,
                    record.EweId,
                    record.RamId,
                    record.BreedingDate,
                    record.PregnancyDays,
                    record.Success
                };

                result.Code = ResultCode.Success;
                result.Message = "配种记录添加成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"添加配种记录失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 更新配种记录
        /// </summary>
        public async Task<APIResult<object>> Handle(UpdateBreedingRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var record = await _context.BreedingRecords.FindAsync(request.RecordId);
                if (record == null)
                {
                    result.Code = ResultCode.NotFound;
                    result.Message = "配种记录不存在";
                    return result;
                }

                // 更新字段
                if (request.BreedingDate.HasValue)
                    record.BreedingDate = request.BreedingDate.Value;
                if (request.PregnancyDays.HasValue)
                    record.PregnancyDays = request.PregnancyDays.Value;
                if (request.Success.HasValue)
                    record.Success = request.Success.Value;

                _context.BreedingRecords.Update(record);
                await _context.SaveChangesAsync(cancellationToken);

                var data = new
                {
                    record.RecordId,
                    record.EweId,
                    record.RamId,
                    record.BreedingDate,
                    record.PregnancyDays,
                    record.Success
                };

                result.Code = ResultCode.Success;
                result.Message = "配种记录更新成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"更新配种记录失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 删除配种记录
        /// </summary>
        public async Task<APIResult<object>> Handle(DeleteBreedingRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var record = await _context.BreedingRecords.FindAsync(request.RecordId);
                if (record == null)
                {
                    result.Code = ResultCode.NotFound;
                    result.Message = "配种记录不存在";
                    return result;
                }

                _context.BreedingRecords.Remove(record);
                await _context.SaveChangesAsync(cancellationToken);

                result.Code = ResultCode.Success;
                result.Message = "配种记录删除成功";
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"删除配种记录失败: {ex.Message}";
            }

            return result;
        }
    }
} 
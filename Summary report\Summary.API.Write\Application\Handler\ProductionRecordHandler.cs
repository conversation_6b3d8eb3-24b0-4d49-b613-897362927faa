using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Write.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Write.Application.Handler
{
    /// <summary>
    /// 生产记录处理器
    /// </summary>
    public class ProductionRecordHandler : 
        IRequestHandler<AddProductionRecordCommand, APIResult<object>>,
        IRequestHandler<UpdateProductionRecordCommand, APIResult<object>>,
        IRequestHandler<DeleteProductionRecordCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public ProductionRecordHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 添加生产记录
        /// </summary>
        public async Task<APIResult<object>> Handle(AddProductionRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                // 验证母羊是否存在
                var ewe = await _context.Sheep.FindAsync(request.EweId);
                if (ewe == null)
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "母羊不存在";
                    return result;
                }

                // 验证母羊是否为母羊
                if (ewe.Gender != "2")
                {
                    result.Code = ResultCode.Fail;
                    result.Message = "只能为母羊添加生产记录";
                    return result;
                }

                var record = new ProductionRecord
                {
                    EweId = request.EweId,
                    ProduceDate = request.ProduceDate,
                    ProduceCount = request.ProduceCount,
                    Issues = request.Issues
                };

                _context.ProductionRecords.Add(record);
                await _context.SaveChangesAsync(cancellationToken);

                var data = new
                {
                    RecordId = record.RecordId,
                    record.EweId,
                    record.ProduceDate,
                    record.ProduceCount,
                    record.Issues
                };

                result.Code = ResultCode.Success;
                result.Message = "生产记录添加成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"添加生产记录失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 更新生产记录
        /// </summary>
        public async Task<APIResult<object>> Handle(UpdateProductionRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var record = await _context.ProductionRecords.FindAsync(request.RecordId);
                if (record == null)
                {
                    result.Code = ResultCode.NotFound;
                    result.Message = "生产记录不存在";
                    return result;
                }

                // 更新字段
                if (request.ProduceDate.HasValue)
                    record.ProduceDate = request.ProduceDate.Value;
                if (request.ProduceCount.HasValue)
                    record.ProduceCount = request.ProduceCount.Value;
                if (request.Issues != null)
                    record.Issues = request.Issues;

                _context.ProductionRecords.Update(record);
                await _context.SaveChangesAsync(cancellationToken);

                var data = new
                {
                    record.RecordId,
                    record.EweId,
                    record.ProduceDate,
                    record.ProduceCount,
                    record.Issues
                };

                result.Code = ResultCode.Success;
                result.Message = "生产记录更新成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"更新生产记录失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 删除生产记录
        /// </summary>
        public async Task<APIResult<object>> Handle(DeleteProductionRecordCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var record = await _context.ProductionRecords.FindAsync(request.RecordId);
                if (record == null)
                {
                    result.Code = ResultCode.NotFound;
                    result.Message = "生产记录不存在";
                    return result;
                }

                _context.ProductionRecords.Remove(record);
                await _context.SaveChangesAsync(cancellationToken);

                result.Code = ResultCode.Success;
                result.Message = "生产记录删除成功";
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"删除生产记录失败: {ex.Message}";
            }

            return result;
        }
    }
} 
# 端口配置说明

## 端口配置概览

### 前端端口
- **前端开发服务器**: `http://localhost:5174`
- **Vite代理**: 自动代理 `/api` 请求到后端服务

### 后端端口
- **Summary.API.Read**: `http://localhost:5251` (统计查询服务)
- **Summary.API.Write**: `http://localhost:5110` (记录管理服务)

## 配置说明

### 1. Vite代理配置
在 `vite.config.ts` 中配置了代理，自动将前端的 `/api` 请求转发到后端：

```typescript
server: {
  port: 5174, // 前端开发服务器端口
  host: true,
  proxy: {
    '/api': {
      target: 'http://localhost:5251', // 默认使用Read API
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '/api')
    }
  }
}
```

### 2. API配置
在 `src/config/api.ts` 中集中管理API配置：

```typescript
export const API_CONFIG = {
  BASE_URL: '/api',
  READ_API_PORT: 5251,
  WRITE_API_PORT: 5110,
  // ...
}
```

### 3. 自动端口检测
系统会自动检测后端服务状态：
- 优先使用 Read API (端口 5251)
- 如果 Read API 不可用，尝试 Write API (端口 5110)
- 如果都不可用，自动切换到模拟数据模式

## 启动顺序

### 1. 启动后端服务
```bash
# 启动 Read API (统计查询服务)
cd "Summary report/Summary.API.Read"
dotnet run

# 启动 Write API (记录管理服务)
cd "Summary report/Summary.API.Write"
dotnet run
```

### 2. 启动前端服务
```bash
cd summaryui
npm run dev
```

## 故障排除

### 端口冲突
如果遇到端口冲突，可以修改以下文件：

1. **修改后端端口**:
   - `Summary.API.Read/Properties/launchSettings.json`
   - `Summary.API.Write/Properties/launchSettings.json`

2. **修改前端代理**:
   - `vite.config.ts` 中的 proxy.target

3. **修改前端端口**:
   - `vite.config.ts` 中的 server.port

### 检查服务状态
前端页面顶部会显示后端服务连接状态：
- 🟢 绿色标签: 后端服务已连接
- 🔴 红色标签: 后端服务未连接

### 使用模拟数据
如果后端服务不可用，系统会自动切换到模拟数据模式，确保前端功能正常运行。

## 开发建议

1. **开发环境**: 使用 Vite 代理，避免跨域问题
2. **生产环境**: 配置 nginx 反向代理或使用完整URL
3. **调试**: 使用浏览器开发者工具查看网络请求
4. **日志**: 查看浏览器控制台和.NET后端日志

## 当前状态

✅ **前端服务**: 运行在 `http://localhost:5174`
✅ **后端服务**: Read API 运行在 `http://localhost:5251`
✅ **代理配置**: 已配置，自动转发 `/api` 请求到后端
✅ **API测试**: 前后端通信正常，返回200状态码
📊 **模拟数据**: 后端不可用时自动启用

## 问题解决状态

### ✅ 已解决的问题
1. **端口号不匹配**: 前端配置的5000端口与后端实际端口5251不匹配
2. **跨域问题**: 通过Vite代理解决
3. **API响应格式**: 正确处理后端的APIResult格式
4. **错误处理**: 实现了优雅的降级到模拟数据

### 🔧 解决方案
1. **统一端口配置**: 使用配置文件集中管理端口设置
2. **Vite代理**: 配置代理自动转发API请求
3. **自动检测**: 前端自动检测后端服务状态
4. **模拟数据**: 后端不可用时自动切换

### 🎯 测试结果
- 后端API: `http://localhost:5251/api/statistics/inventory/default-farm` ✅ 200 OK
- 前端代理: `http://localhost:5174/api/statistics/inventory/default-farm` ✅ 200 OK
- 数据格式: 正确解析APIResult响应 ✅
- 错误处理: 自动降级到模拟数据 ✅ 
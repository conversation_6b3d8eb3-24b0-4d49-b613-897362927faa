<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Summary.API.Write</name>
    </assembly>
    <members>
        <member name="T:Summary.API.Write.Application.command.AddBreedingRecordCommand">
            <summary>
            添加配种记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddBreedingRecordCommand.EweId">
            <summary>
            母羊ID
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddBreedingRecordCommand.RamId">
            <summary>
            公羊ID
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddBreedingRecordCommand.BreedingDate">
            <summary>
            配种日期
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddBreedingRecordCommand.PregnancyDays">
            <summary>
            妊娠天数
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddBreedingRecordCommand.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddBreedingRecordCommand.Notes">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.command.UpdateBreedingRecordCommand">
            <summary>
            更新配种记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateBreedingRecordCommand.RecordId">
            <summary>
            记录ID
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateBreedingRecordCommand.BreedingDate">
            <summary>
            配种日期
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateBreedingRecordCommand.PregnancyDays">
            <summary>
            妊娠天数
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateBreedingRecordCommand.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateBreedingRecordCommand.Notes">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.command.DeleteBreedingRecordCommand">
            <summary>
            删除配种记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.DeleteBreedingRecordCommand.RecordId">
            <summary>
            记录ID
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.command.AddLambingRecordCommand">
            <summary>
            添加产羔记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddLambingRecordCommand.EweId">
            <summary>
            母羊ID
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddLambingRecordCommand.LambingDate">
            <summary>
            产羔日期
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddLambingRecordCommand.LambCount">
            <summary>
            产羔数量
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddLambingRecordCommand.SurvivalRate">
            <summary>
            存活率
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddLambingRecordCommand.Notes">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.command.UpdateLambingRecordCommand">
            <summary>
            更新产羔记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateLambingRecordCommand.RecordId">
            <summary>
            记录ID
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateLambingRecordCommand.LambingDate">
            <summary>
            产羔日期
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateLambingRecordCommand.LambCount">
            <summary>
            产羔数量
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateLambingRecordCommand.SurvivalRate">
            <summary>
            存活率
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateLambingRecordCommand.Notes">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.command.DeleteLambingRecordCommand">
            <summary>
            删除产羔记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.DeleteLambingRecordCommand.RecordId">
            <summary>
            记录ID
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.command.AddProductionRecordCommand">
            <summary>
            添加生产记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddProductionRecordCommand.EweId">
            <summary>
            母羊ID
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddProductionRecordCommand.ProduceDate">
            <summary>
            生产日期
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddProductionRecordCommand.ProduceCount">
            <summary>
            生产数量
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddProductionRecordCommand.Issues">
            <summary>
            问题描述
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.AddProductionRecordCommand.Notes">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.command.UpdateProductionRecordCommand">
            <summary>
            更新生产记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateProductionRecordCommand.RecordId">
            <summary>
            记录ID
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateProductionRecordCommand.ProduceDate">
            <summary>
            生产日期
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateProductionRecordCommand.ProduceCount">
            <summary>
            生产数量
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateProductionRecordCommand.Issues">
            <summary>
            问题描述
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.UpdateProductionRecordCommand.Notes">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.command.DeleteProductionRecordCommand">
            <summary>
            删除生产记录命令
            </summary>
        </member>
        <member name="P:Summary.API.Write.Application.command.DeleteProductionRecordCommand.RecordId">
            <summary>
            记录ID
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.Handler.BreedingRecordHandler">
            <summary>
            配种记录处理器
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.BreedingRecordHandler.Handle(Summary.API.Write.Application.command.AddBreedingRecordCommand,System.Threading.CancellationToken)">
            <summary>
            添加配种记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.BreedingRecordHandler.Handle(Summary.API.Write.Application.command.UpdateBreedingRecordCommand,System.Threading.CancellationToken)">
            <summary>
            更新配种记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.BreedingRecordHandler.Handle(Summary.API.Write.Application.command.DeleteBreedingRecordCommand,System.Threading.CancellationToken)">
            <summary>
            删除配种记录
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.Handler.LambingRecordHandler">
            <summary>
            产羔记录处理器
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.LambingRecordHandler.Handle(Summary.API.Write.Application.command.AddLambingRecordCommand,System.Threading.CancellationToken)">
            <summary>
            添加产羔记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.LambingRecordHandler.Handle(Summary.API.Write.Application.command.UpdateLambingRecordCommand,System.Threading.CancellationToken)">
            <summary>
            更新产羔记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.LambingRecordHandler.Handle(Summary.API.Write.Application.command.DeleteLambingRecordCommand,System.Threading.CancellationToken)">
            <summary>
            删除产羔记录
            </summary>
        </member>
        <member name="T:Summary.API.Write.Application.Handler.ProductionRecordHandler">
            <summary>
            生产记录处理器
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.ProductionRecordHandler.Handle(Summary.API.Write.Application.command.AddProductionRecordCommand,System.Threading.CancellationToken)">
            <summary>
            添加生产记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.ProductionRecordHandler.Handle(Summary.API.Write.Application.command.UpdateProductionRecordCommand,System.Threading.CancellationToken)">
            <summary>
            更新生产记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Application.Handler.ProductionRecordHandler.Handle(Summary.API.Write.Application.command.DeleteProductionRecordCommand,System.Threading.CancellationToken)">
            <summary>
            删除生产记录
            </summary>
        </member>
        <member name="T:Summary.API.Write.Controllers.RecordsController">
            <summary>
            记录管理控制器
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.AddLambingRecord(Summary.API.Write.Application.command.AddLambingRecordCommand)">
            <summary>
            添加产羔记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.UpdateLambingRecord(System.Int64,Summary.API.Write.Application.command.UpdateLambingRecordCommand)">
            <summary>
            更新产羔记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.DeleteLambingRecord(System.Int64)">
            <summary>
            删除产羔记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.AddBreedingRecord(Summary.API.Write.Application.command.AddBreedingRecordCommand)">
            <summary>
            添加配种记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.UpdateBreedingRecord(System.Int64,Summary.API.Write.Application.command.UpdateBreedingRecordCommand)">
            <summary>
            更新配种记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.DeleteBreedingRecord(System.Int64)">
            <summary>
            删除配种记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.AddProductionRecord(Summary.API.Write.Application.command.AddProductionRecordCommand)">
            <summary>
            添加生产记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.UpdateProductionRecord(System.Int64,Summary.API.Write.Application.command.UpdateProductionRecordCommand)">
            <summary>
            更新生产记录
            </summary>
        </member>
        <member name="M:Summary.API.Write.Controllers.RecordsController.DeleteProductionRecord(System.Int64)">
            <summary>
            删除生产记录
            </summary>
        </member>
    </members>
</doc>

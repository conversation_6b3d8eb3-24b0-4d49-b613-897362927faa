using MediatR;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.command
{
    /// <summary>
    /// 获取羊场存栏统计命令
    /// </summary>
    public class GetInventoryStatisticsCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 农场ID
        /// </summary>
        public string FarmId { get; set; } = string.Empty;
        
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
    }

    /// <summary>
    /// 获取羊场存栏历史趋势命令
    /// </summary>
    public class GetInventoryHistoryTrendCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 农场ID
        /// </summary>
        public string FarmId { get; set; } = string.Empty;
        
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }
        
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }
    }

    /// <summary>
    /// 生成羊场存栏报表命令
    /// </summary>
    public class GenerateInventoryReportCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 农场ID
        /// </summary>
        public string FarmId { get; set; } = string.Empty;
        
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
    }
} 
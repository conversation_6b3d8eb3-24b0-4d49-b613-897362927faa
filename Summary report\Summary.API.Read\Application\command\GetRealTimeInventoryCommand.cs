using MediatR;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.command
{
    /// <summary>
    /// 获取羊场实时存栏命令
    /// </summary>
    public class GetRealTimeInventoryCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 农场ID
        /// </summary>
        public string FarmId { get; set; } = string.Empty;
        
        /// <summary>
        /// 显示模式 - list: 列表模式, map: 地图模式
        /// </summary>
        public string? DisplayMode { get; set; } = "list";
    }

    /// <summary>
    /// 获取羊只位置地图命令
    /// </summary>
    public class GetSheepLocationMapCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 农场ID
        /// </summary>
        public string FarmId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取羊只详情命令
    /// </summary>
    public class GetSheepDetailsCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 羊只ID
        /// </summary>
        public string SheepId { get; set; } = string.Empty;
    }
} 
using MediatR;
using Microsoft.EntityFrameworkCore;
using Summary.Domain.Entities;
using Summary.Infrastructure;
using Summary.API.Read.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Read.Application.Handler
{
    /// <summary>
    /// 羊场存栏统计处理器
    /// </summary>
    public class InventoryStatisticsHandler : 
        IRequestHandler<GetInventoryStatisticsCommand, APIResult<object>>,
        IRequestHandler<GetInventoryHistoryTrendCommand, APIResult<object>>,
        IRequestHandler<GenerateInventoryReportCommand, APIResult<object>>
    {
        private readonly MyDbContext _context;

        public InventoryStatisticsHandler(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取羊场存栏统计
        /// </summary>
        public async Task<APIResult<object>> Handle(GetInventoryStatisticsCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var query = _context.Sheep.Where(s => s.FarmId == request.FarmId);

                // 按时间段筛选
                if (request.StartDate.HasValue)
                {
                    query = query.Where(s => s.BirthDate >= request.StartDate.Value);
                }
                if (request.EndDate.HasValue)
                {
                    query = query.Where(s => s.BirthDate <= request.EndDate.Value);
                }

                var sheepList = await query.ToListAsync(cancellationToken);

                // 按品种分组统计
                var breedStats = sheepList
                    .GroupBy(s => s.Breed)
                    .Select(g => new
                    {
                        Breed = g.Key,
                        TotalCount = g.Count(),
                        MaleCount = g.Count(s => s.Gender == "1"),
                        FemaleCount = g.Count(s => s.Gender == "2"),
                        HealthyCount = g.Count(s => s.Health == "Healthy"),
                        SickCount = g.Count(s => s.Health == "Sick"),
                        PregnantCount = g.Count(s => s.Health == "Pregnant")
                    })
                    .ToList();

                // 按性别分组统计
                var genderStats = sheepList
                    .GroupBy(s => s.Gender)
                    .Select(g => new
                    {
                        Gender = g.Key == "1" ? "公羊" : "母羊",
                        Count = g.Count()
                    })
                    .ToList();

                // 按年龄分组统计
                var ageStats = sheepList
                    .Where(s => s.BirthDate.HasValue)
                    .GroupBy(s => GetAgeGroup(s.BirthDate!.Value))
                    .Select(g => new
                    {
                        AgeGroup = g.Key,
                        Count = g.Count()
                    })
                    .ToList();

                var data = new
                {
                    TotalSheep = sheepList.Count,
                    BreedStatistics = breedStats,
                    GenderStatistics = genderStats,
                    AgeStatistics = ageStats,
                    QueryPeriod = new
                    {
                        StartDate = request.StartDate,
                        EndDate = request.EndDate
                    }
                };

                result.Code = ResultCode.Success;
                result.Message = "获取羊场存栏统计成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取羊场存栏统计失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 获取羊场存栏历史趋势
        /// </summary>
        public async Task<APIResult<object>> Handle(GetInventoryHistoryTrendCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var monthlyStats = new List<object>();
                var currentDate = request.StartDate;

                while (currentDate <= request.EndDate)
                {
                    var monthStart = new DateTime(currentDate.Year, currentDate.Month, 1);
                    var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                    var sheepCount = await _context.Sheep
                        .Where(s => s.FarmId == request.FarmId && s.BirthDate <= monthEnd)
                        .CountAsync(cancellationToken);

                    monthlyStats.Add(new
                    {
                        Month = monthStart.ToString("yyyy-MM"),
                        SheepCount = sheepCount
                    });

                    currentDate = currentDate.AddMonths(1);
                }

                var data = new
                {
                    FarmId = request.FarmId,
                    TrendData = monthlyStats
                };

                result.Code = ResultCode.Success;
                result.Message = "获取羊场存栏历史趋势成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"获取羊场存栏历史趋势失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 生成羊场存栏报表
        /// </summary>
        public async Task<APIResult<object>> Handle(GenerateInventoryReportCommand request, CancellationToken cancellationToken)
        {
            var result = new APIResult<object>();
            
            try
            {
                var statisticsCommand = new GetInventoryStatisticsCommand
                {
                    FarmId = request.FarmId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate
                };

                var statisticsResult = await Handle(statisticsCommand, cancellationToken);
                
                var data = new
                {
                    ReportType = "羊场存栏统计报表",
                    GeneratedTime = DateTime.Now,
                    FarmId = request.FarmId,
                    Statistics = statisticsResult.Data
                };

                result.Code = ResultCode.Success;
                result.Message = "生成羊场存栏报表成功";
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"生成羊场存栏报表失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 获取年龄分组
        /// </summary>
        private string GetAgeGroup(DateTime birthDate)
        {
            var age = DateTime.Now.Year - birthDate.Year;
            if (age < 1) return "幼羊(0-1岁)";
            if (age < 3) return "青年羊(1-3岁)";
            if (age < 6) return "成年羊(3-6岁)";
            return "老年羊(6岁以上)";
        }
    }
} 
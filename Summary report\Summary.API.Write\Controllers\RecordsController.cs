using MediatR;
using Microsoft.AspNetCore.Mvc;
using Summary.API.Write.Application.command;
using Summary.ErrorCode;

namespace Summary.API.Write.Controllers
{
    /// <summary>
    /// 记录管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class RecordsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public RecordsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        #region 产羔记录管理

        /// <summary>
        /// 添加产羔记录
        /// </summary>
        [HttpPost("lambing")]
        public async Task<APIResult<object>> AddLambingRecord([FromBody] AddLambingRecordCommand request)
        {
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 更新产羔记录
        /// </summary>
        [HttpPut("lambing/{recordId}")]
        public async Task<APIResult<object>> UpdateLambingRecord(
            long recordId,
            [FromBody] UpdateLambingRecordCommand request)
        {
            request.RecordId = recordId;
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 删除产羔记录
        /// </summary>
        [HttpDelete("lambing/{recordId}")]
        public async Task<APIResult<object>> DeleteLambingRecord(long recordId)
        {
            var request = new DeleteLambingRecordCommand
            {
                RecordId = recordId
            };

            return await _mediator.Send(request);
        }

        #endregion

        #region 配种记录管理

        /// <summary>
        /// 添加配种记录
        /// </summary>
        [HttpPost("breeding")]
        public async Task<APIResult<object>> AddBreedingRecord([FromBody] AddBreedingRecordCommand request)
        {
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 更新配种记录
        /// </summary>
        [HttpPut("breeding/{recordId}")]
        public async Task<APIResult<object>> UpdateBreedingRecord(
            long recordId,
            [FromBody] UpdateBreedingRecordCommand request)
        {
            request.RecordId = recordId;
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 删除配种记录
        /// </summary>
        [HttpDelete("breeding/{recordId}")]
        public async Task<APIResult<object>> DeleteBreedingRecord(long recordId)
        {
            var request = new DeleteBreedingRecordCommand
            {
                RecordId = recordId
            };

            return await _mediator.Send(request);
        }

        #endregion

        #region 生产记录管理

        /// <summary>
        /// 添加生产记录
        /// </summary>
        [HttpPost("production")]
        public async Task<APIResult<object>> AddProductionRecord([FromBody] AddProductionRecordCommand request)
        {
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 更新生产记录
        /// </summary>
        [HttpPut("production/{recordId}")]
        public async Task<APIResult<object>> UpdateProductionRecord(
            long recordId,
            [FromBody] UpdateProductionRecordCommand request)
        {
            request.RecordId = recordId;
            return await _mediator.Send(request);
        }

        /// <summary>
        /// 删除生产记录
        /// </summary>
        [HttpDelete("production/{recordId}")]
        public async Task<APIResult<object>> DeleteProductionRecord(long recordId)
        {
            var request = new DeleteProductionRecordCommand
            {
                RecordId = recordId
            };

            return await _mediator.Send(request);
        }

        #endregion
    }
} 
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Summary.ErrorCode
{
    /// <summary>
    /// API统一返回结果封装类 - 用于标准化API响应格式
    /// </summary>
    /// <typeparam name="T">返回数据的类型</typeparam>
    public class APIResult<T>
    {
        /// <summary>
        /// 返回码 - 标识操作是否成功，如200成功、500失败等
        /// </summary>
        public ResultCode Code { get; set; }
        
        /// <summary>
        /// 消息 - 返回给客户端的提示信息，如"操作成功"、"参数错误"等
        /// </summary>
        public string Message { get; set; } = string.Empty;
        
        /// <summary>
        /// 访问令牌 - 用于身份验证的JWT Token
        /// </summary>
        public string Token { get; set; } = string.Empty;
        
        /// <summary>
        /// 刷新令牌 - 用于刷新访问令牌的Refresh Token
        /// </summary>
        public string RefreshToken { get; set; } = string.Empty;
        
        /// <summary>
        /// 返回数据 - 具体的业务数据内容
        /// </summary>
        public T Data { get; set; } = default(T)!;
    }
}

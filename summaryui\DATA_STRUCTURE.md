# 前端数据结构完善说明

## 概述

根据后端的数据结构，我们完善了前端的数据类型定义，确保前后端数据格式的一致性，提供了完整的类型安全和数据转换功能。

## 完善内容

### 1. 类型定义 (`src/types/index.ts`)

#### 枚举定义
- **SheepGender**: 羊只性别枚举 (Male=1, Female=2)
- **SheepHealthStatus**: 健康状态枚举 (Healthy, Sick, Pregnant, Recovering)
- **BreedingSuccess**: 配种成功状态枚举 (Failed=0, Success=1)
- **ExportFormat**: 报表导出格式枚举 (PDF, Excel, Word)

#### 实体类型
- **Sheep**: 羊只信息实体，包含所有属性和导航属性
- **LambingRecord**: 产羔记录实体
- **BreedingRecord**: 配种记录实体
- **ProductionRecord**: 生产记录实体

#### API响应类型
- **APIResult<T>**: 统一API响应格式
- **APIPaging<T>**: 分页结果格式

#### 统计数据类型
- **InventoryStatistics**: 存栏统计数据类型
- **LambingStatistics**: 产羔统计数据类型
- **BreedingStatistics**: 配种统计数据类型
- **ProductionStatistics**: 生产统计数据类型

#### 实时数据类型
- **RealTimeInventory**: 实时存栏数据类型
- **SheepDetails**: 羊只详情数据类型

#### 表单数据类型
- **AddLambingRecordForm**: 添加产羔记录表单
- **AddBreedingRecordForm**: 添加配种记录表单
- **AddProductionRecordForm**: 添加生产记录表单

#### 查询参数类型
- **InventoryQueryParams**: 存栏统计查询参数
- **LambingQueryParams**: 产羔统计查询参数
- **BreedingQueryParams**: 配种统计查询参数
- **ProductionQueryParams**: 生产统计查询参数

### 2. 数据转换工具 (`src/utils/dataTransform.ts`)

#### 后端数据转换
- **transformBackendData.sheep()**: 转换羊只数据
- **transformBackendData.lambingRecord()**: 转换产羔记录数据
- **transformBackendData.breedingRecord()**: 转换配种记录数据
- **transformBackendData.productionRecord()**: 转换生产记录数据
- **transformBackendData.inventoryStatistics()**: 转换存栏统计数据
- **transformBackendData.lambingStatistics()**: 转换产羔统计数据
- **transformBackendData.breedingStatistics()**: 转换配种统计数据
- **transformBackendData.productionStatistics()**: 转换生产统计数据
- **transformBackendData.realTimeInventory()**: 转换实时存栏数据

#### 数据格式化工具
- **formatData.gender()**: 格式化性别显示
- **formatData.health()**: 格式化健康状态显示
- **formatData.date()**: 格式化日期
- **formatData.dateTime()**: 格式化日期时间
- **formatData.percentage()**: 格式化百分比
- **formatData.number()**: 格式化数字
- **formatData.breedColor()**: 获取品种颜色

#### 数据验证工具
- **validateData.sheepId()**: 验证羊只ID格式
- **validateData.date()**: 验证日期格式
- **validateData.count()**: 验证数量范围
- **validateData.survivalRate()**: 验证存活率范围
- **validateData.pregnancyDays()**: 验证怀孕天数范围

#### 数据计算工具
- **calculateData.age()**: 计算年龄
- **calculateData.averageSurvivalRate()**: 计算平均存活率
- **calculateData.successRate()**: 计算成功率
- **calculateData.growthRate()**: 计算增长率

### 3. 模拟数据生成器 (`src/utils/mockData.ts`)

完全匹配后端数据结构的模拟数据生成器：
- **generateMockData.inventoryStatistics()**: 生成存栏统计数据
- **generateMockData.lambingStatistics()**: 生成产羔统计数据
- **generateMockData.breedingStatistics()**: 生成配种统计数据
- **generateMockData.productionStatistics()**: 生成生产统计数据
- **generateMockData.realTimeInventory()**: 生成实时存栏数据

### 4. API Store更新 (`src/stores/api.ts`)

- 使用完整的类型定义
- 集成数据转换工具
- 提供类型安全的API调用
- 支持模拟数据和真实API的自动切换

## 数据结构映射

### 后端到前端的数据映射

| 后端字段 | 前端字段 | 说明 |
|---------|---------|------|
| SheepId | sheepId | 羊只ID |
| FarmId | farmId | 农场ID |
| Breed | breed | 品种 |
| Gender | gender | 性别 (1:公羊, 2:母羊) |
| BirthDate | birthDate | 出生日期 |
| Health | health | 健康状态 |
| KeeperId | keeperId | 饲养员ID |
| Location | location | 位置信息 |
| TotalSheep | totalSheep | 总存栏数 |
| BreedStatistics | breedStatistics | 品种统计 |
| GenderStatistics | genderStatistics | 性别统计 |
| AgeStatistics | ageStatistics | 年龄统计 |

### 枚举值映射

#### 性别映射
```typescript
const GenderDisplayMap: Record<string, string> = {
  '1': '公羊',
  '2': '母羊'
}
```

#### 健康状态映射
```typescript
const HealthDisplayMap: Record<string, string> = {
  'Healthy': '健康',
  'Sick': '生病',
  'Pregnant': '怀孕',
  'Recovering': '康复中'
}
```

#### 品种颜色映射
```typescript
const BreedColorMap: Record<string, string> = {
  '杜泊': '#3498db',
  '澳洲白': '#2ecc71',
  '萨福克': '#f1c40f',
  '杜湖': '#e74c3c',
  '澳湖': '#9b59b6'
}
```

## 使用示例

### 1. 在组件中使用类型定义

```typescript
import type { Sheep, InventoryStatistics } from '@/types'

const sheep: Sheep = {
  sheepId: 'S001',
  farmId: 'farm-001',
  breed: '杜泊',
  gender: '1',
  health: 'Healthy',
  keeperId: 'K001'
}

const inventoryStats: InventoryStatistics = {
  totalSheep: 136,
  breedStatistics: [],
  genderStatistics: [],
  ageStatistics: [],
  queryPeriod: {}
}
```

### 2. 使用数据转换工具

```typescript
import { transformBackendData, formatData } from '@/utils/dataTransform'

// 转换后端数据
const frontendData = transformBackendData.sheep(backendData)

// 格式化显示
const genderDisplay = formatData.gender('1') // '公羊'
const healthDisplay = formatData.health('Healthy') // '健康'
const dateDisplay = formatData.date('2023-12-15') // '2023/12/15'
```

### 3. 使用数据验证工具

```typescript
import { validateData } from '@/utils/dataTransform'

const isValidSheepId = validateData.sheepId('S001') // true
const isValidDate = validateData.date('2023-12-15') // true
const isValidCount = validateData.count(5, 0, 10) // true
```

### 4. 使用数据计算工具

```typescript
import { calculateData } from '@/utils/dataTransform'

const age = calculateData.age('2020-01-01') // 3
const successRate = calculateData.successRate(100, 85) // 85
const growthRate = calculateData.growthRate(120, 100) // 20
```

## 优势

### 1. 类型安全
- 完整的TypeScript类型定义
- 编译时类型检查
- IDE智能提示和自动补全

### 2. 数据一致性
- 前后端数据结构完全匹配
- 自动数据格式转换
- 统一的字段命名规范

### 3. 开发效率
- 可复用的数据转换工具
- 标准化的格式化函数
- 完善的验证和计算工具

### 4. 维护性
- 集中管理的数据类型定义
- 模块化的工具函数
- 清晰的代码结构

## 注意事项

1. **字段命名**: 前端使用camelCase，后端使用PascalCase，转换工具自动处理
2. **数据类型**: 确保前后端数据类型一致，特别是日期、数字等类型
3. **可选字段**: 使用可选属性处理可能为空的字段
4. **默认值**: 为数值类型提供合理的默认值
5. **错误处理**: 在数据转换过程中处理异常情况

## 总结

通过完善前端数据结构，我们实现了：

✅ **完整的类型定义**: 覆盖所有后端实体和API响应
✅ **数据转换工具**: 自动处理后端到前端的数据格式转换
✅ **格式化工具**: 统一的数据显示格式
✅ **验证工具**: 数据有效性检查
✅ **计算工具**: 常用的数据计算函数
✅ **类型安全**: 完整的TypeScript支持
✅ **开发效率**: 提高开发效率和代码质量

现在前端系统具备了完整的数据处理能力，可以安全、高效地与后端API进行交互。 